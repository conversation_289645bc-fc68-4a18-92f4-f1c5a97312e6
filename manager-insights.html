<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 研发中心管理者关注点分析</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        .section h2 {
            color: #303133;
            border-bottom: 2px solid #409eff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .insight-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .insight-card {
            background: #f8f9fa;
            border-left: 4px solid #409eff;
            padding: 20px;
            border-radius: 8px;
        }
        .insight-card h4 {
            color: #409eff;
            margin-bottom: 10px;
        }
        .priority-high { border-left-color: #f56c6c; }
        .priority-medium { border-left-color: #e6a23c; }
        .priority-low { border-left-color: #67c23a; }
        .metric-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .metric-table th, .metric-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ebeef5;
        }
        .metric-table th {
            background: #f5f7fa;
            font-weight: 600;
        }
        .dashboard-preview {
            background: #f8f9fa;
            border: 2px dashed #409eff;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #409eff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 5px;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #337ecc;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 研发中心管理者关注点分析</h1>
        <p>基于项目投入数据的管理决策支持体系</p>
    </div>

    <div class="section">
        <h2>🔍 管理者核心关注点</h2>
        <p>作为研发中心管理者，我需要从<strong>战略高度</strong>和<strong>运营效率</strong>两个维度来关注团队和项目的整体情况：</p>
        
        <div class="insight-grid">
            <div class="insight-card priority-high">
                <h4>🎯 战略执行监控</h4>
                <ul>
                    <li><strong>关键项目进展</strong>：重点项目是否按计划推进</li>
                    <li><strong>资源配置合理性</strong>：人力是否投入到最有价值的项目</li>
                    <li><strong>技术债务控制</strong>：是否在追求速度的同时保证质量</li>
                    <li><strong>创新投入比例</strong>：新技术研发vs维护性工作的平衡</li>
                </ul>
            </div>
            
            <div class="insight-card priority-high">
                <h4>💰 成本效益分析</h4>
                <ul>
                    <li><strong>人力成本控制</strong>：月度投入是否在预算范围内</li>
                    <li><strong>项目ROI评估</strong>：投入产出比是否达到预期</li>
                    <li><strong>资源利用率</strong>：团队成员是否充分发挥价值</li>
                    <li><strong>外包vs自研</strong>：成本结构是否合理</li>
                </ul>
            </div>
            
            <div class="insight-card priority-medium">
                <h4>⚡ 团队效能优化</h4>
                <ul>
                    <li><strong>跨部门协作</strong>：是否存在协作瓶颈</li>
                    <li><strong>人员负载均衡</strong>：避免关键人员过载</li>
                    <li><strong>技能发展轨迹</strong>：团队能力是否持续提升</li>
                    <li><strong>工作满意度</strong>：团队士气和稳定性</li>
                </ul>
            </div>
            
            <div class="insight-card priority-medium">
                <h4>🚨 风险预警机制</h4>
                <ul>
                    <li><strong>进度风险识别</strong>：提前发现可能延期的项目</li>
                    <li><strong>质量风险监控</strong>：技术债务和缺陷趋势</li>
                    <li><strong>人员流失风险</strong>：关键人员离职预警</li>
                    <li><strong>技术风险评估</strong>：新技术引入的风险控制</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>📊 基于现有数据的关键指标设计</h2>
        <p>根据<code>project_employee_input</code>表的数据结构，我设计了以下管理仪表盘指标：</p>
        
        <table class="metric-table">
            <thead>
                <tr>
                    <th>指标类别</th>
                    <th>具体指标</th>
                    <th>计算方式</th>
                    <th>管理价值</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td rowspan="4"><strong>资源配置</strong></td>
                    <td>部门人力分布</td>
                    <td>按first_dept_name统计人员数量</td>
                    <td>识别资源配置是否合理</td>
                </tr>
                <tr>
                    <td>项目投入集中度</td>
                    <td>按dev_proj_id统计投入人月</td>
                    <td>发现资源过度集中的风险</td>
                </tr>
                <tr>
                    <td>外包vs正式员工比例</td>
                    <td>按emp_type统计投入占比</td>
                    <td>优化用工结构和成本</td>
                </tr>
                <tr>
                    <td>人员利用率</td>
                    <td>per_days/标准工作日</td>
                    <td>识别人员负载和闲置情况</td>
                </tr>
                <tr>
                    <td rowspan="3"><strong>项目健康度</strong></td>
                    <td>项目投入偏差</td>
                    <td>实际投入vs预估投入对比</td>
                    <td>及时发现预算超支风险</td>
                </tr>
                <tr>
                    <td>项目进度健康度</td>
                    <td>基于投入进度推算项目状态</td>
                    <td>提前预警延期风险</td>
                </tr>
                <tr>
                    <td>团队规模合理性</td>
                    <td>项目团队人数vs项目复杂度</td>
                    <td>优化团队配置效率</td>
                </tr>
                <tr>
                    <td rowspan="3"><strong>趋势分析</strong></td>
                    <td>月度投入趋势</td>
                    <td>按year+month统计总投入</td>
                    <td>预测未来资源需求</td>
                </tr>
                <tr>
                    <td>部门负载变化</td>
                    <td>各部门月度投入变化率</td>
                    <td>动态调整部门资源</td>
                </tr>
                <tr>
                    <td>项目生命周期</td>
                    <td>项目从启动到结束的投入曲线</td>
                    <td>优化项目管理流程</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>🎛️ 仪表盘设计理念</h2>
        
        <div class="highlight">
            <strong>💡 设计原则：</strong> 
            "一屏掌控全局，三秒发现问题，五分钟制定决策"
        </div>
        
        <div class="insight-grid">
            <div class="insight-card">
                <h4>📈 顶层概览</h4>
                <p><strong>核心KPI展示</strong></p>
                <ul>
                    <li>在研项目总数</li>
                    <li>研发人员总数</li>
                    <li>月度总投入(人月)</li>
                    <li>平均团队效率</li>
                </ul>
            </div>
            
            <div class="insight-card">
                <h4>🎯 资源配置视图</h4>
                <p><strong>人力资源分布分析</strong></p>
                <ul>
                    <li>各部门人员占比饼图</li>
                    <li>项目投入热力图</li>
                    <li>人员负载分布</li>
                    <li>外包比例监控</li>
                </ul>
            </div>
            
            <div class="insight-card">
                <h4>🚦 项目健康度</h4>
                <p><strong>项目状态一目了然</strong></p>
                <ul>
                    <li>健康/风险/延期项目分布</li>
                    <li>投入偏差TOP项目</li>
                    <li>关键路径项目状态</li>
                    <li>资源冲突预警</li>
                </ul>
            </div>
            
            <div class="insight-card">
                <h4>📊 趋势预测</h4>
                <p><strong>数据驱动决策</strong></p>
                <ul>
                    <li>6个月投入趋势</li>
                    <li>效率变化曲线</li>
                    <li>成本预测模型</li>
                    <li>容量规划建议</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>⚠️ 关键预警机制</h2>
        <p>基于数据分析的智能预警系统，帮助管理者提前识别和应对风险：</p>
        
        <div class="insight-grid">
            <div class="insight-card priority-high">
                <h4>🔴 高优先级预警</h4>
                <ul>
                    <li><strong>项目延期风险</strong>：投入进度 < 时间进度 15%</li>
                    <li><strong>预算超支风险</strong>：实际投入 > 预估投入 110%</li>
                    <li><strong>关键人员过载</strong>：月投入 > 标准工时 120%</li>
                    <li><strong>质量风险</strong>：投入密度过高，可能影响质量</li>
                </ul>
            </div>
            
            <div class="insight-card priority-medium">
                <h4>🟡 中等优先级预警</h4>
                <ul>
                    <li><strong>资源配置不均</strong>：部门间负载差异 > 30%</li>
                    <li><strong>项目投入集中</strong>：单项目投入 > 总投入 40%</li>
                    <li><strong>外包依赖过高</strong>：外包投入 > 总投入 60%</li>
                    <li><strong>新项目启动风险</strong>：当前负载已达 85%</li>
                </ul>
            </div>
            
            <div class="insight-card priority-low">
                <h4>🔵 低优先级提醒</h4>
                <ul>
                    <li><strong>资源优化机会</strong>：某些项目可提前完成</li>
                    <li><strong>技能发展建议</strong>：团队成员技能提升机会</li>
                    <li><strong>流程优化建议</strong>：基于数据的效率提升点</li>
                    <li><strong>成本优化机会</strong>：资源配置优化建议</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🚀 快速行动建议</h2>
        <p>基于仪表盘数据，为管理者提供可执行的行动建议：</p>
        
        <div class="dashboard-preview">
            <h3>📋 管理者日常工作流</h3>
            <p><strong>每日5分钟</strong> → 查看关键指标和预警信息</p>
            <p><strong>每周30分钟</strong> → 深入分析趋势和资源配置</p>
            <p><strong>每月2小时</strong> → 全面评估和战略调整</p>
            
            <div style="margin-top: 20px;">
                <a href="dashboard-home.html" class="btn">🎯 查看管理仪表盘</a>
                <a href="person-analysis.html" class="btn">👥 人员维度分析</a>
                <a href="project-analysis.html" class="btn">📊 项目维度分析</a>
                <a href="department-analysis.html" class="btn">🏢 部门维度分析</a>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>💡 数据驱动的管理价值</h2>
        <div class="insight-grid">
            <div class="insight-card">
                <h4>🎯 精准决策</h4>
                <p>基于真实数据而非主观判断，提高决策的准确性和可执行性。</p>
            </div>
            
            <div class="insight-card">
                <h4>⚡ 快速响应</h4>
                <p>实时监控和预警机制，让管理者能够快速识别和应对问题。</p>
            </div>
            
            <div class="insight-card">
                <h4>📈 持续优化</h4>
                <p>通过历史数据分析，不断优化资源配置和管理流程。</p>
            </div>
            
            <div class="insight-card">
                <h4>🤝 透明沟通</h4>
                <p>数据可视化促进团队间的透明沟通和协作。</p>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时的交互效果
        window.onload = function() {
            console.log('🎯 管理者关注点分析页面已加载');
            
            // 添加卡片悬停效果
            const cards = document.querySelectorAll('.insight-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 20px rgba(0,0,0,0.1)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });
        };
    </script>
</body>
</html>
