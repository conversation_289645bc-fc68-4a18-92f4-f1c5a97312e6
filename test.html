<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘测试页面</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            padding: 20px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ebeef5;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .api-result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status-success {
            color: #67c23a;
        }
        
        .status-error {
            color: #f56c6c;
        }
        
        .loading {
            color: #409eff;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>研发中心仪表盘 - API测试</h1>
            <p>此页面用于测试API连接和数据获取功能</p>
            
            <div class="test-section">
                <div class="test-title">📊 日报数据测试</div>
                <el-button @click="testDailyReports" :loading="loading.daily" type="primary">
                    测试日报API
                </el-button>
                <div class="api-result" v-if="results.daily">
                    <div :class="results.daily.success ? 'status-success' : 'status-error'">
                        状态: {{ results.daily.success ? '成功' : '失败' }}
                    </div>
                    <div>数据量: {{ results.daily.count || 0 }} 条</div>
                    <div v-if="results.daily.error">错误: {{ results.daily.error }}</div>
                    <div v-if="results.daily.sample">
                        样本数据: <pre>{{ JSON.stringify(results.daily.sample, null, 2) }}</pre>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <div class="test-title">📋 计划数据测试</div>
                <el-button @click="testPlanData" :loading="loading.plan" type="primary">
                    测试计划API
                </el-button>
                <div class="api-result" v-if="results.plan">
                    <div :class="results.plan.success ? 'status-success' : 'status-error'">
                        状态: {{ results.plan.success ? '成功' : '失败' }}
                    </div>
                    <div>数据量: {{ results.plan.count || 0 }} 条</div>
                    <div v-if="results.plan.error">错误: {{ results.plan.error }}</div>
                    <div v-if="results.plan.sample">
                        样本数据: <pre>{{ JSON.stringify(results.plan.sample, null, 2) }}</pre>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <div class="test-title">🚀 项目数据测试</div>
                <el-button @click="testProjectData" :loading="loading.project" type="primary">
                    测试项目API
                </el-button>
                <div class="api-result" v-if="results.project">
                    <div :class="results.project.success ? 'status-success' : 'status-error'">
                        状态: {{ results.project.success ? '成功' : '失败' }}
                    </div>
                    <div>数据量: {{ results.project.count || 0 }} 条</div>
                    <div v-if="results.project.error">错误: {{ results.project.error }}</div>
                    <div v-if="results.project.sample">
                        样本数据: <pre>{{ JSON.stringify(results.project.sample, null, 2) }}</pre>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <div class="test-title">🔄 全部测试</div>
                <el-button @click="testAll" :loading="isTestingAll" type="success" size="large">
                    运行全部测试
                </el-button>
                <div v-if="allTestsComplete" style="margin-top: 15px;">
                    <el-alert 
                        :title="allTestsSuccess ? '所有测试通过' : '部分测试失败'" 
                        :type="allTestsSuccess ? 'success' : 'error'"
                        show-icon>
                    </el-alert>
                </div>
            </div>
            
            <div class="test-section">
                <div class="test-title">🔗 快速链接</div>
                <el-button @click="openDashboard" type="info">
                    打开完整仪表盘
                </el-button>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <script>
        const { createApp, ref, reactive, computed } = Vue;
        const { ElMessage } = ElementPlus;
        
        // API配置
        const API_CONFIG = {
            baseURL: 'https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/model/list',
            headers: {
                'Content-Type': 'application/json',
                'x-funipaas-authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJTTTNXaXRoU00yIn0.eyJ1YXRfdWlkIjoiNGZlNGFlNDMwNGQ5OWQyZmE5YWRmZDAwNGY2OWVlNWE5NTkxMDZlYjM5YWM3YzFjZDlkNGY5ODkzZmI2MmEzOTVjYjAyYWFiMzQzODQwNGZiMjE1ZGMwMjMyYzY3MmYxIiwidWF0X3NpZCI6ImJiODNiZWExM2Q3ZWM2YjBiZWNjMWEwNTkzMjZlMDg3YjNkMGMzN2I0NmU0ZWZjNmZlMGZlODc0MWE3Y2IxNDJiYWE4OWMyZjgyNmY3Y2QxZTBkOTg3NGM2MGEwMjhkNzc2YzcyMmExZWRhZTUzNWUzM2JiZDZmNWZmMDQ5OGUwOWViMzc2ODRjOWQ3MDFkODdkMDQwMGZlZjYyY2ZjNzk4ZGRjZjQ3NWYzNTA5MTA3ZWJlZDdkNjI2ZjBlOTEwZTNjYmEyOGJjMGViZGU2Y2M5NDJjYzFjZTBkM2Q5ODkzIiwiZXhwIjoxNzU0MTE5NTIwfQ.MEYCIQDz8CsA8sKxU6tZK8cQBrlSb5MP1eRz5ZFIrJx8FqLi_wIhAPaop-6KyxjNG1BusTzN03_VmfdDdLUNBKKp8eqlRu9B',
                'x-funipaas-request-hash': 'ce3b082aa9783a6bef7c71470699f003bf51c7622b9052554eb4073375f08a20',
                'x-funipaas-request-id': 'f0cc6871-56c0-219e-8f5c-11aa2957a301',
                'x-funipaas-tenant': 'dev'
            }
        };
        
        // API请求参数
        const API_PARAMS = {
            dailyReports: {
                "pageSize": 10,
                "pageNo": 1,
                "pageIndex": 1,
                "page_id": "785761401783296",
                "extendData": {},
                "tabType": "model",
                "model_id": "647a312d-ce90-4968-b159-5b531f85bc53",
                "sort": [{"key": "create_time", "mode": "desc"}],
                "component_id": "16cf3d7c-a537-1ec2-a69f-93e282ece000",
                "params": [{"logicalOperator": "", "conditions": [{"key": "is_deleted", "operator": "EQUAL", "value": "0", "logicalOperator": ""}]}],
                "joinFields": ["daily_info"],
                "filter": []
            },
            planData: {
                "pageSize": 10,
                "pageNo": 1,
                "pageIndex": 1,
                "page_id": "12734221815051265",
                "extendData": {},
                "tabType": "model",
                "model_id": "7fd9e661-8a61-451a-a9aa-7d8ff21aecb7",
                "sort": [],
                "component_id": "4171130f-b0af-5a6c-f09f-acf14502051b",
                "params": [{"logicalOperator": "", "conditions": [{"key": "is_deleted", "operator": "EQUAL", "value": "0", "logicalOperator": ""}]}],
                "joinFields": ["plan_info"],
                "filter": []
            },
            devProjects: {
                "pageSize": 10,
                "pageNo": 1,
                "pageIndex": 1,
                "page_id": "4313042434582529",
                "extendData": {},
                "tabType": "model",
                "model_id": "ad96ce63-35b0-4c0d-b2e6-42c9f8a793d8",
                "sort": [{"serialNumber": 1, "uuid": "ff0ca1a2-2f61-95a9-cbb0-582fe0156533", "key": "dev_proj_no", "mode": "desc"}],
                "component_id": "c2264b60-870f-220c-206b-e39ea1e97b4c",
                "params": [
                    {"logicalOperator": "", "conditions": [{"key": "dept_id", "operator": "LIKE", "value": ["17f0b34e-e41a-4782-8fbb-488874fbe7cc", "eeb003cd-52a2-4963-90cf-29e3dbe975da"], "logicalOperator": "AND"}, {"key": "is_deleted", "operator": "EQUAL", "value": "0", "logicalOperator": "AND"}]}
                ],
                "joinFields": [],
                "filter": []
            }
        };
        
        const app = createApp({
            setup() {
                const loading = reactive({
                    daily: false,
                    plan: false,
                    project: false
                });
                
                const results = reactive({
                    daily: null,
                    plan: null,
                    project: null
                });
                
                const isTestingAll = ref(false);
                const allTestsComplete = ref(false);
                
                const allTestsSuccess = computed(() => {
                    return results.daily?.success && results.plan?.success && results.project?.success;
                });
                
                // API测试函数
                const testAPI = async (params, type) => {
                    try {
                        const response = await fetch(API_CONFIG.baseURL, {
                            method: 'POST',
                            headers: API_CONFIG.headers,
                            body: JSON.stringify(params)
                        });
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        const data = await response.json();
                        
                        if (data.status === 200 && data.success) {
                            const list = data.data?.list || [];
                            results[type] = {
                                success: true,
                                count: list.length,
                                sample: list[0] || null
                            };
                            ElMessage.success(`${type} API测试成功`);
                        } else {
                            throw new Error(data.message || '请求失败');
                        }
                    } catch (error) {
                        results[type] = {
                            success: false,
                            error: error.message
                        };
                        ElMessage.error(`${type} API测试失败: ${error.message}`);
                    }
                };
                
                const testDailyReports = async () => {
                    loading.daily = true;
                    await testAPI(API_PARAMS.dailyReports, 'daily');
                    loading.daily = false;
                };
                
                const testPlanData = async () => {
                    loading.plan = true;
                    await testAPI(API_PARAMS.planData, 'plan');
                    loading.plan = false;
                };
                
                const testProjectData = async () => {
                    loading.project = true;
                    await testAPI(API_PARAMS.devProjects, 'project');
                    loading.project = false;
                };
                
                const testAll = async () => {
                    isTestingAll.value = true;
                    allTestsComplete.value = false;
                    
                    await Promise.all([
                        testDailyReports(),
                        testPlanData(),
                        testProjectData()
                    ]);
                    
                    isTestingAll.value = false;
                    allTestsComplete.value = true;
                };
                
                const openDashboard = () => {
                    window.open('index.html', '_blank');
                };
                
                return {
                    loading,
                    results,
                    isTestingAll,
                    allTestsComplete,
                    allTestsSuccess,
                    testDailyReports,
                    testPlanData,
                    testProjectData,
                    testAll,
                    openDashboard
                };
            }
        });
        
        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>
