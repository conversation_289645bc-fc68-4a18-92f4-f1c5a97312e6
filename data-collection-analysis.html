<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 仪表盘数据采集需求分析报告</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        .section h2 {
            color: #303133;
            border-bottom: 2px solid #409eff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .analysis-card {
            background: #f8f9fa;
            border-left: 4px solid #409eff;
            padding: 20px;
            border-radius: 8px;
        }
        .analysis-card h4 {
            color: #409eff;
            margin-bottom: 10px;
        }
        .status-satisfied { border-left-color: #67c23a; }
        .status-partial { border-left-color: #e6a23c; }
        .status-missing { border-left-color: #f56c6c; }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        .data-table th, .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ebeef5;
        }
        .data-table th {
            background: #f5f7fa;
            font-weight: 600;
        }
        .sql-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .sql-comment {
            color: #68d391;
        }
        .priority-high {
            background: #fef0f0;
            border-left-color: #f56c6c;
        }
        .priority-medium {
            background: #fdf6ec;
            border-left-color: #e6a23c;
        }
        .priority-low {
            background: #f0f9ff;
            border-left-color: #409eff;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #409eff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 5px;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #337ecc;
        }
        .status-icon {
            font-size: 18px;
            margin-right: 8px;
        }
        .implementation-timeline {
            background: #f8f9fa;
            border: 2px solid #409eff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .timeline-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
        }
        .timeline-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            color: white;
        }
        .phase-1 { background: #f56c6c; }
        .phase-2 { background: #e6a23c; }
        .phase-3 { background: #409eff; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 仪表盘数据采集需求分析报告</h1>
        <p>基于现有表结构的数据完整性评估与补充方案</p>
    </div>

    <div class="section">
        <h2>🔍 现有数据结构评估</h2>
        <p>基于 <code>model_4l0zlfamkrh</code> (研发项目表) 和 <code>project_employee_input</code> (项目员工投入表) 的数据完整性分析：</p>

        <div class="analysis-grid">
            <div class="analysis-card status-satisfied">
                <h4><span class="status-icon">✅</span>已满足的指标 (70%)</h4>
                <ul>
                    <li><strong>基础统计</strong>：项目数量、人员数量、投入统计</li>
                    <li><strong>资源配置</strong>：部门分布、员工类型分析</li>
                    <li><strong>项目属性</strong>：重要程度、复杂度、预估投入</li>
                    <li><strong>成本预估</strong>：预估成本和投入人月</li>
                </ul>
            </div>

            <div class="analysis-card status-partial">
                <h4><span class="status-icon">⚠️</span>部分满足的指标 (20%)</h4>
                <ul>
                    <li><strong>项目状态</strong>：有状态字段但缺少健康度评估</li>
                    <li><strong>时间管理</strong>：有开始结束时间但缺少进度跟踪</li>
                    <li><strong>人员效率</strong>：有投入天数但缺少效率评分</li>
                    <li><strong>成本控制</strong>：有预估成本但缺少实际成本</li>
                </ul>
            </div>

            <div class="analysis-card status-missing">
                <h4><span class="status-icon">❌</span>缺失的关键指标 (10%)</h4>
                <ul>
                    <li><strong>实时进度</strong>：项目完成百分比、里程碑状态</li>
                    <li><strong>质量指标</strong>：代码质量、缺陷率、测试覆盖率</li>
                    <li><strong>风险预警</strong>：自动化风险识别和预警机制</li>
                    <li><strong>团队绩效</strong>：个人效率、满意度、技能评估</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>📋 现有数据可支持的仪表盘指标</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th>指标类别</th>
                    <th>具体指标</th>
                    <th>数据来源</th>
                    <th>计算方式</th>
                    <th>完整度</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td rowspan="4"><strong>基础统计</strong></td>
                    <td>在研项目数量</td>
                    <td>model_4l0zlfamkrh</td>
                    <td>COUNT(DISTINCT dev_proj_id) WHERE dev_proj_status='进行中'</td>
                    <td><span style="color: #67c23a;">✅ 100%</span></td>
                </tr>
                <tr>
                    <td>研发人员数量</td>
                    <td>project_employee_input</td>
                    <td>COUNT(DISTINCT emp_no)</td>
                    <td><span style="color: #67c23a;">✅ 100%</span></td>
                </tr>
                <tr>
                    <td>月度投入人月</td>
                    <td>project_employee_input</td>
                    <td>SUM(per_days) / 22</td>
                    <td><span style="color: #67c23a;">✅ 100%</span></td>
                </tr>
                <tr>
                    <td>平均效率</td>
                    <td>project_employee_input</td>
                    <td>需要补充效率评分字段</td>
                    <td><span style="color: #f56c6c;">❌ 0%</span></td>
                </tr>
                <tr>
                    <td rowspan="3"><strong>资源配置</strong></td>
                    <td>部门人员分布</td>
                    <td>project_employee_input</td>
                    <td>GROUP BY first_dept_name</td>
                    <td><span style="color: #67c23a;">✅ 100%</span></td>
                </tr>
                <tr>
                    <td>外包vs正式比例</td>
                    <td>project_employee_input</td>
                    <td>GROUP BY emp_type</td>
                    <td><span style="color: #67c23a;">✅ 100%</span></td>
                </tr>
                <tr>
                    <td>人员利用率</td>
                    <td>project_employee_input</td>
                    <td>per_days / 标准工作日</td>
                    <td><span style="color: #e6a23c;">⚠️ 80%</span></td>
                </tr>
                <tr>
                    <td rowspan="3"><strong>项目健康度</strong></td>
                    <td>项目状态分布</td>
                    <td>model_4l0zlfamkrh</td>
                    <td>GROUP BY dev_proj_status</td>
                    <td><span style="color: #e6a23c;">⚠️ 60%</span></td>
                </tr>
                <tr>
                    <td>投入偏差分析</td>
                    <td>两表关联</td>
                    <td>实际投入 vs dev_proj_imm</td>
                    <td><span style="color: #f56c6c;">❌ 30%</span></td>
                </tr>
                <tr>
                    <td>进度完成率</td>
                    <td>缺失</td>
                    <td>需要新增进度跟踪字段</td>
                    <td><span style="color: #f56c6c;">❌ 0%</span></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>🚨 需要补充的关键数据结构</h2>

        <div class="highlight">
            <strong>💡 核心问题：</strong>
            现有数据结构主要支持"投入统计"，但缺少"效果评估"和"风险预警"的关键数据
        </div>

        <div class="analysis-grid">
            <div class="analysis-card priority-high">
                <h4>🔴 高优先级补充 (立即实施)</h4>
                <p><strong>项目进度和状态跟踪</strong></p>
                <div class="sql-block">
<span class="sql-comment">-- 在研发项目表中添加进度跟踪字段</span>
ALTER TABLE model_4l0zlfamkrh
ADD COLUMN dev_proj_progress DECIMAL(5,2) COMMENT '项目完成进度(0-100)',
ADD COLUMN dev_proj_health_status VARCHAR(20) COMMENT '健康状态(健康/风险/延期)',
ADD COLUMN dev_proj_actual_imm DECIMAL(10,2) COMMENT '实际投入人月',
ADD COLUMN dev_proj_actual_cost DECIMAL(20,2) COMMENT '实际投入成本';
                </div>
                <p><strong>采集时机：</strong>项目经理每周更新，系统根据投入数据自动计算</p>
            </div>

            <div class="analysis-card priority-high">
                <h4>🔴 高优先级补充 (立即实施)</h4>
                <p><strong>资源利用率精确计算</strong></p>
                <div class="sql-block">
<span class="sql-comment">-- 在投入表中添加利用率计算字段</span>
ALTER TABLE project_employee_input
ADD COLUMN standard_workdays DECIMAL(5,2) DEFAULT 22 COMMENT '标准工作天数',
ADD COLUMN actual_workdays DECIMAL(5,2) COMMENT '实际工作天数',
ADD COLUMN utilization_rate DECIMAL(5,2) COMMENT '利用率(%)',
ADD COLUMN billable_days DECIMAL(5,2) COMMENT '可计费天数';
                </div>
                <p><strong>采集时机：</strong>每月人事系统自动计算并同步</p>
            </div>

            <div class="analysis-card priority-medium">
                <h4>🟡 中优先级补充 (3个月内)</h4>
                <p><strong>员工绩效和效率评估</strong></p>
                <div class="sql-block">
<span class="sql-comment">-- 新建员工绩效表</span>
CREATE TABLE employee_performance (
  id VARCHAR(100) NOT NULL PRIMARY KEY,
  emp_no VARCHAR(50) NOT NULL COMMENT '员工编号',
  year INT COMMENT '年份',
  month INT COMMENT '月份',
  efficiency_score DECIMAL(5,2) COMMENT '效率评分(0-100)',
  quality_score DECIMAL(5,2) COMMENT '质量评分(0-100)',
  workload_ratio DECIMAL(5,2) COMMENT '工作负载比例',
  satisfaction_score DECIMAL(3,1) COMMENT '满意度(1-5)',
  skill_level VARCHAR(20) COMMENT '技能等级'
) COMMENT='员工绩效表';
                </div>
                <p><strong>采集时机：</strong>效率评分系统自动计算，满意度每月员工自评</p>
            </div>

            <div class="analysis-card priority-medium">
                <h4>🟡 中优先级补充 (3个月内)</h4>
                <p><strong>项目里程碑和任务跟踪</strong></p>
                <div class="sql-block">
<span class="sql-comment">-- 新建项目里程碑表</span>
CREATE TABLE project_milestones (
  id VARCHAR(100) NOT NULL PRIMARY KEY,
  dev_proj_id VARCHAR(100) NOT NULL COMMENT '研发项目ID',
  milestone_name VARCHAR(200) COMMENT '里程碑名称',
  milestone_type VARCHAR(50) COMMENT '类型(需求/设计/开发/测试)',
  planned_date DATE COMMENT '计划完成时间',
  actual_date DATE COMMENT '实际完成时间',
  status VARCHAR(20) COMMENT '状态(未开始/进行中/已完成)',
  completion_rate DECIMAL(5,2) COMMENT '完成率(%)',
  risk_level VARCHAR(20) COMMENT '风险等级(低/中/高)'
) COMMENT='项目里程碑表';
                </div>
                <p><strong>采集时机：</strong>项目启动时创建，项目经理每周更新状态</p>
            </div>

            <div class="analysis-card priority-low">
                <h4>🔵 低优先级补充 (6个月内)</h4>
                <p><strong>智能预警和风险管理</strong></p>
                <div class="sql-block">
<span class="sql-comment">-- 新建风险预警表</span>
CREATE TABLE risk_alerts (
  id VARCHAR(100) NOT NULL PRIMARY KEY,
  alert_type VARCHAR(50) COMMENT '预警类型(进度/成本/质量/人员)',
  alert_level VARCHAR(20) COMMENT '预警级别(低/中/高)',
  related_type VARCHAR(20) COMMENT '关联对象类型(项目/员工/部门)',
  related_id VARCHAR(100) COMMENT '关联对象ID',
  alert_title VARCHAR(200) COMMENT '预警标题',
  alert_description TEXT COMMENT '预警描述',
  alert_status VARCHAR(20) COMMENT '状态(活跃/已处理/已忽略)',
  suggested_action TEXT COMMENT '建议措施'
) COMMENT='风险预警表';
                </div>
                <p><strong>采集时机：</strong>系统每日自动扫描生成，管理者处理后更新状态</p>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>📈 数据采集流程设计</h2>

        <div class="analysis-grid">
            <div class="analysis-card">
                <h4>🤖 自动采集 (系统计算)</h4>
                <ul>
                    <li><strong>项目进度</strong>：根据里程碑完成情况自动计算</li>
                    <li><strong>利用率</strong>：per_days / standard_workdays * 100</li>
                    <li><strong>成本偏差</strong>：(actual_cost - planned_cost) / planned_cost * 100</li>
                    <li><strong>进度偏差</strong>：根据里程碑计划vs实际时间计算</li>
                    <li><strong>风险预警</strong>：根据预设规则自动生成预警</li>
                </ul>
                <p><strong>频率：</strong>每日凌晨自动执行</p>
            </div>

            <div class="analysis-card">
                <h4>👤 手工录入 (定期更新)</h4>
                <ul>
                    <li><strong>项目健康度</strong>：项目经理每周评估</li>
                    <li><strong>员工满意度</strong>：员工每月自评</li>
                    <li><strong>技能等级</strong>：半年度技能评估</li>
                    <li><strong>里程碑状态</strong>：项目经理每周更新</li>
                    <li><strong>质量评分</strong>：代码审查和测试结果</li>
                </ul>
                <p><strong>责任人：</strong>项目经理、HR、员工本人</p>
            </div>

            <div class="analysis-card">
                <h4>🔗 第三方集成 (实时同步)</h4>
                <ul>
                    <li><strong>考勤数据</strong>：从考勤系统获取实际工作天数</li>
                    <li><strong>代码指标</strong>：从Git仓库获取代码质量指标</li>
                    <li><strong>任务完成</strong>：从项目管理工具获取任务状态</li>
                    <li><strong>缺陷数据</strong>：从缺陷管理系统获取质量数据</li>
                    <li><strong>构建部署</strong>：从CI/CD系统获取发布频率</li>
                </ul>
                <p><strong>技术方案：</strong>API接口 + 定时同步</p>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🎯 实施路线图</h2>

        <div class="implementation-timeline">
            <h3>📅 分阶段实施计划</h3>

            <div class="timeline-item">
                <div class="timeline-icon phase-1">1</div>
                <div>
                    <h4>第一阶段：基础数据补充 (1-2周)</h4>
                    <p><strong>目标：</strong>让现有仪表盘从模拟数据升级为真实数据</p>
                    <ul>
                        <li>✅ 添加项目进度跟踪字段 (dev_proj_progress, dev_proj_health_status)</li>
                        <li>✅ 添加实际投入统计字段 (dev_proj_actual_imm, dev_proj_actual_cost)</li>
                        <li>✅ 添加资源利用率计算字段 (utilization_rate, actual_workdays)</li>
                        <li>✅ 开发数据同步脚本，从投入表计算实际投入</li>
                    </ul>
                    <p><strong>预期效果：</strong>仪表盘显示真实的项目投入和进度数据</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-icon phase-2">2</div>
                <div>
                    <h4>第二阶段：绩效和预警系统 (1-2个月)</h4>
                    <p><strong>目标：</strong>建立完整的绩效评估和风险预警机制</p>
                    <ul>
                        <li>📊 创建员工绩效表 (employee_performance)</li>
                        <li>🚨 创建风险预警表 (risk_alerts)</li>
                        <li>📅 创建项目里程碑表 (project_milestones)</li>
                        <li>🤖 开发自动预警算法和规则引擎</li>
                        <li>📱 开发移动端数据录入界面</li>
                    </ul>
                    <p><strong>预期效果：</strong>实现智能预警和全面的绩效管理</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-icon phase-3">3</div>
                <div>
                    <h4>第三阶段：系统集成和优化 (2-3个月)</h4>
                    <p><strong>目标：</strong>实现数据自动化采集和高级分析功能</p>
                    <ul>
                        <li>🔗 集成考勤系统、代码仓库、项目管理工具</li>
                        <li>📈 开发预测分析和趋势预测模型</li>
                        <li>🎨 优化仪表盘界面和用户体验</li>
                        <li>📊 添加高级分析报表和导出功能</li>
                        <li>🔒 完善权限管理和数据安全</li>
                    </ul>
                    <p><strong>预期效果：</strong>实现完全自动化的数据驱动管理平台</p>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>💰 投入产出分析</h2>

        <table class="data-table">
            <thead>
                <tr>
                    <th>实施阶段</th>
                    <th>开发工作量</th>
                    <th>数据质量提升</th>
                    <th>管理效率提升</th>
                    <th>ROI评估</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>第一阶段</strong></td>
                    <td>2人周</td>
                    <td>从30%提升到70%</td>
                    <td>基础数据可视化</td>
                    <td><span style="color: #67c23a;">高</span></td>
                </tr>
                <tr>
                    <td><strong>第二阶段</strong></td>
                    <td>6人周</td>
                    <td>从70%提升到90%</td>
                    <td>智能预警和绩效管理</td>
                    <td><span style="color: #67c23a;">高</span></td>
                </tr>
                <tr>
                    <td><strong>第三阶段</strong></td>
                    <td>8人周</td>
                    <td>从90%提升到95%</td>
                    <td>全自动化管理平台</td>
                    <td><span style="color: #e6a23c;">中</span></td>
                </tr>
            </tbody>
        </table>

        <div class="highlight">
            <strong>💡 建议：</strong>
            优先实施第一阶段，快速获得管理价值；第二阶段根据使用效果决定投入规模；第三阶段可作为长期规划逐步实施。
        </div>
    </div>

    <div class="section">
        <h2>🚀 立即行动建议</h2>

        <div class="analysis-grid">
            <div class="analysis-card priority-high">
                <h4>🎯 本周可执行</h4>
                <ul>
                    <li>✅ 执行第一阶段的SQL脚本，添加必要字段</li>
                    <li>✅ 开发简单的数据同步脚本</li>
                    <li>✅ 更新仪表盘，使用真实数据替换模拟数据</li>
                    <li>✅ 培训项目经理如何录入项目进度数据</li>
                </ul>
            </div>

            <div class="analysis-card priority-medium">
                <h4>📋 本月规划</h4>
                <ul>
                    <li>📊 设计员工绩效评估流程和标准</li>
                    <li>🚨 定义风险预警规则和阈值</li>
                    <li>📱 设计移动端数据录入界面原型</li>
                    <li>🔗 调研第三方系统集成的技术方案</li>
                </ul>
            </div>

            <div class="analysis-card priority-low">
                <h4>🔮 长期愿景</h4>
                <ul>
                    <li>🤖 基于AI的项目风险预测</li>
                    <li>📈 个性化的员工发展建议</li>
                    <li>🎯 智能的资源配置优化</li>
                    <li>📊 行业对标和最佳实践推荐</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="dashboard-home.html" class="btn">🎯 查看当前仪表盘</a>
            <a href="manager-insights.html" class="btn">📋 管理者关注点</a>
            <a href="#" class="btn" onclick="downloadSQLScript()">💾 下载SQL脚本</a>
        </div>
    </div>

    <script>
        // 页面交互功能
        window.onload = function() {
            console.log('📊 数据采集需求分析报告已加载');

            // 添加卡片悬停效果
            const cards = document.querySelectorAll('.analysis-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 20px rgba(0,0,0,0.1)';
                    this.style.transition = 'all 0.2s ease';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });
        };

        function downloadSQLScript() {
            // 生成SQL脚本内容
            const sqlContent = `-- 仪表盘数据采集补充脚本
-- 第一阶段：基础数据补充

-- 1. 在研发项目表中添加进度跟踪字段
ALTER TABLE model_4l0zlfamkrh
ADD COLUMN dev_proj_progress DECIMAL(5,2) COMMENT '项目完成进度百分比(0-100)',
ADD COLUMN dev_proj_health_status VARCHAR(20) COMMENT '项目健康状态(健康/风险/延期)',
ADD COLUMN dev_proj_actual_imm DECIMAL(10,2) COMMENT '实际投入人月',
ADD COLUMN dev_proj_actual_cost DECIMAL(20,2) COMMENT '实际投入成本',
ADD COLUMN dev_proj_planned_end_date DATE COMMENT '计划结束时间',
ADD COLUMN dev_proj_estimated_end_date DATE COMMENT '预估结束时间';

-- 2. 在投入表中添加利用率计算字段
ALTER TABLE project_employee_input
ADD COLUMN standard_workdays DECIMAL(5,2) DEFAULT 22 COMMENT '标准工作天数',
ADD COLUMN actual_workdays DECIMAL(5,2) COMMENT '实际工作天数',
ADD COLUMN utilization_rate DECIMAL(5,2) COMMENT '利用率(%)',
ADD COLUMN billable_days DECIMAL(5,2) COMMENT '可计费天数';

-- 3. 创建员工绩效表
CREATE TABLE employee_performance (
  id VARCHAR(100) NOT NULL PRIMARY KEY,
  emp_no VARCHAR(50) NOT NULL COMMENT '员工编号',
  emp_name VARCHAR(100) COMMENT '员工姓名',
  year INT COMMENT '年份',
  month INT COMMENT '月份',
  efficiency_score DECIMAL(5,2) COMMENT '效率评分(0-100)',
  quality_score DECIMAL(5,2) COMMENT '质量评分(0-100)',
  workload_ratio DECIMAL(5,2) COMMENT '工作负载比例(0-200)',
  overtime_hours DECIMAL(8,2) COMMENT '加班小时数',
  task_completion_rate DECIMAL(5,2) COMMENT '任务完成率(%)',
  satisfaction_score DECIMAL(3,1) COMMENT '工作满意度(1-5)',
  skill_level VARCHAR(20) COMMENT '技能等级(初级/中级/高级/专家)',
  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工绩效表';

-- 4. 创建项目里程碑表
CREATE TABLE project_milestones (
  id VARCHAR(100) NOT NULL PRIMARY KEY,
  dev_proj_id VARCHAR(100) NOT NULL COMMENT '研发项目ID',
  milestone_name VARCHAR(200) COMMENT '里程碑名称',
  milestone_type VARCHAR(50) COMMENT '里程碑类型(需求/设计/开发/测试/上线)',
  planned_date DATE COMMENT '计划完成时间',
  actual_date DATE COMMENT '实际完成时间',
  status VARCHAR(20) COMMENT '状态(未开始/进行中/已完成/延期)',
  completion_rate DECIMAL(5,2) COMMENT '完成率(%)',
  risk_level VARCHAR(20) COMMENT '风险等级(低/中/高)',
  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目里程碑表';

-- 5. 创建风险预警表
CREATE TABLE risk_alerts (
  id VARCHAR(100) NOT NULL PRIMARY KEY,
  alert_type VARCHAR(50) COMMENT '预警类型(进度/成本/质量/人员)',
  alert_level VARCHAR(20) COMMENT '预警级别(低/中/高)',
  related_type VARCHAR(20) COMMENT '关联对象类型(项目/员工/部门)',
  related_id VARCHAR(100) COMMENT '关联对象ID',
  alert_title VARCHAR(200) COMMENT '预警标题',
  alert_description TEXT COMMENT '预警描述',
  alert_status VARCHAR(20) COMMENT '预警状态(活跃/已处理/已忽略)',
  trigger_condition TEXT COMMENT '触发条件',
  suggested_action TEXT COMMENT '建议措施',
  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  resolved_time TIMESTAMP NULL COMMENT '解决时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风险预警表';`;

            // 创建下载链接
            const blob = new Blob([sqlContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'dashboard_data_enhancement.sql';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            alert('SQL脚本已下载！请在数据库中执行以补充必要的数据结构。');
        }
    </script>
</body>
</html>