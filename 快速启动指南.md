# 🚀 投入分析系统 - 快速启动指南

## ❗ 重要说明
由于浏览器安全限制，直接双击打开HTML文件（file://协议）无法访问HTTPS API，会出现404错误。
**解决方案：需要通过HTTP服务器运行页面。**

## 🎯 最简单的启动方法

### 方法1: 一键启动脚本 (推荐)

#### macOS/Linux:
```bash
# 在终端中执行
./start-server.sh
```

#### Windows:
```bash
# 双击运行
start-server.bat
```

### 方法2: 手动启动Python服务器

#### 步骤1: 打开终端
- **macOS**: 按 `Cmd + 空格`，输入"终端"
- **Windows**: 按 `Win + R`，输入"cmd"

#### 步骤2: 进入项目目录
```bash
cd /Users/<USER>/Desktop/用户体验部/ai/vs-augment/app5-投入分析
```

#### 步骤3: 启动HTTP服务器
```bash
# Python 3 (推荐)
python3 -m http.server 8080

# 或者 Python 2
python -m SimpleHTTPServer 8080
```

#### 步骤4: 打开浏览器访问
```
http://localhost:8080/person-analysis.html
http://localhost:8080/project-analysis.html
http://localhost:8080/department-analysis.html
```

## 🔧 其他启动方法

### VS Code Live Server (如果使用VS Code)
1. 安装"Live Server"插件
2. 右键HTML文件 → "Open with Live Server"

### Node.js http-server
```bash
# 安装 (一次性)
npm install -g http-server

# 启动
http-server -p 8080 --cors
```

## 📋 访问地址

启动服务器后，在浏览器中访问：

- **人员维度分析**: http://localhost:8080/person-analysis.html
- **项目维度分析**: http://localhost:8080/project-analysis.html  
- **部门维度分析**: http://localhost:8080/department-analysis.html
- **API连接测试**: http://localhost:8080/api-test.html
- **Mock数据演示**: http://localhost:8080/mock-data-fallback.html

## ❓ 常见问题

### Q: 为什么直接打开HTML文件不行？
A: 浏览器的同源策略限制，file://协议无法访问https://的API接口。

### Q: Python命令不存在怎么办？
A: 
1. 安装Python: https://www.python.org/downloads/
2. 或使用VS Code Live Server插件
3. 或使用Node.js http-server

### Q: 端口8080被占用怎么办？
A: 修改端口号，如使用8081:
```bash
python3 -m http.server 8081
```

### Q: API仍然404怎么办？
A: 
1. 确保使用http://localhost访问（不是file://）
2. 打开API测试页面检查接口状态
3. 检查认证token是否过期

## 🎉 成功标志

当看到以下信息时，表示启动成功：
```
Serving HTTP on 0.0.0.0 port 8080 (http://0.0.0.0:8080/) ...
```

然后在浏览器中访问 http://localhost:8080/person-analysis.html 即可正常使用！

## 🛑 停止服务器

- **终端**: 按 `Ctrl + C`
- **脚本**: 按提示操作停止
