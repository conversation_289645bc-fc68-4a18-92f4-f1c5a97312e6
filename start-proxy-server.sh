#!/bin/bash

echo "🚀 启动投入分析系统 (代理模式)"
echo "================================"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js"
    echo ""
    echo "请先安装Node.js:"
    echo "1. 访问 https://nodejs.org/"
    echo "2. 下载并安装LTS版本"
    echo "3. 重新运行此脚本"
    echo ""
    exit 1
fi

echo "✅ 找到Node.js版本: $(node --version)"

# 检查npm是否可用
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到npm"
    exit 1
fi

echo "✅ 找到npm版本: $(npm --version)"

# 检查package.json是否存在
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到package.json文件"
    echo "请确保在正确的目录中运行此脚本"
    exit 1
fi

# 检查依赖是否已安装
if [ ! -d "node_modules" ]; then
    echo "📦 正在安装依赖包..."
    npm install
    
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        echo ""
        echo "请手动安装依赖:"
        echo "npm install"
        exit 1
    fi
    
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖包已存在"
fi

echo ""
echo "🔧 启动代理服务器..."

# 启动Node.js代理服务器
node proxy-server.js &
SERVER_PID=$!

echo "📡 代理服务器PID: $SERVER_PID"
echo "⏳ 等待服务器启动..."
sleep 3

# 检查服务器是否启动成功
if kill -0 $SERVER_PID 2>/dev/null; then
    echo ""
    echo "🌐 代理服务器已启动成功!"
    echo "================================"
    echo "📱 页面访问地址:"
    echo "   人员分析: http://localhost:3000/person-analysis.html"
    echo "   项目分析: http://localhost:3000/project-analysis.html"
    echo "   部门分析: http://localhost:3000/department-analysis.html"
    echo "   API测试:  http://localhost:3000/api-test.html"
    echo ""
    echo "🔗 API代理信息:"
    echo "   代理地址: http://localhost:3000/api"
    echo "   目标地址: https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/sql/list"
    echo "   健康检查: http://localhost:3000/health"
    echo ""
    
    # 尝试自动打开浏览器
    if command -v open &> /dev/null; then
        echo "🔗 自动打开浏览器..."
        open http://localhost:3000/person-analysis.html
    elif command -v xdg-open &> /dev/null; then
        echo "🔗 自动打开浏览器..."
        xdg-open http://localhost:3000/person-analysis.html
    else
        echo "💡 请手动在浏览器中打开: http://localhost:3000/person-analysis.html"
    fi
    
    echo ""
    echo "💡 使用说明:"
    echo "1. 页面会自动使用代理访问API，解决跨域问题"
    echo "2. 所有API请求都通过本地代理转发到目标服务器"
    echo "3. 保持原有的认证信息和请求格式"
    echo ""
    echo "🛑 按 Ctrl+C 停止服务器"
    echo "================================"
    
    # 设置信号处理，优雅关闭服务器
    trap "echo ''; echo '🛑 正在停止代理服务器...'; kill $SERVER_PID 2>/dev/null; echo '✅ 代理服务器已停止'; exit 0" INT TERM
    
    # 等待服务器进程
    wait $SERVER_PID
    
else
    echo "❌ 代理服务器启动失败"
    echo ""
    echo "可能的原因:"
    echo "1. 端口3000被占用"
    echo "2. 依赖包未正确安装"
    echo "3. proxy-server.js文件不存在"
    echo ""
    echo "解决方案:"
    echo "1. 检查端口占用: lsof -i :3000"
    echo "2. 重新安装依赖: rm -rf node_modules && npm install"
    echo "3. 检查文件是否存在: ls -la proxy-server.js"
    exit 1
fi
