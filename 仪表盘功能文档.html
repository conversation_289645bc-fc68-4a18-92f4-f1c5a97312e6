<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>&#x7814;&#x53d1;&#x4e2d;&#x5fc3;&#x4eea;&#x8868;&#x76d8;&#x529f;&#x80fd;&#x6e05;&#x5355;</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1 id="研发中心仪表盘功能清单">研发中心仪表盘功能清单</h1>
<h2 id="概述">概述</h2>
<p>基于现有数据结构，为研发中心负责人设计的综合管理仪表盘，涵盖任务管理、人员管理、项目管理、资源配置等核心功能。</p>
<h2 id="功能清单">功能清单</h2>
<h3 id="1--总览仪表盘">1. 📊 总览仪表盘</h3>
<p><strong>功能描述</strong>：研发中心整体运营状况概览
<strong>数据计算方式</strong>：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// 项目总数统计</span>
<span class="hljs-keyword">const</span> projectStats = {
  <span class="hljs-attr">total</span>: devProjects.<span class="hljs-property">length</span>,
  <span class="hljs-attr">inProgress</span>: devProjects.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> p.<span class="hljs-property">dev_proj_status</span> === <span class="hljs-string">&#x27;in_prog&#x27;</span>).<span class="hljs-property">length</span>,
  <span class="hljs-attr">completed</span>: devProjects.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> p.<span class="hljs-property">dev_proj_status</span> === <span class="hljs-string">&#x27;completed&#x27;</span>).<span class="hljs-property">length</span>,
  <span class="hljs-attr">paused</span>: devProjects.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> p.<span class="hljs-property">dev_proj_status</span> === <span class="hljs-string">&#x27;paused&#x27;</span>).<span class="hljs-property">length</span>
}

<span class="hljs-comment">// 人员总数统计</span>
<span class="hljs-keyword">const</span> staffStats = {
  <span class="hljs-attr">total</span>: uniqueStaff.<span class="hljs-property">length</span>,
  <span class="hljs-attr">byDept</span>: <span class="hljs-title function_">groupBy</span>(dailyReports, <span class="hljs-string">&#x27;first_dept_name&#x27;</span>),
  <span class="hljs-attr">byType</span>: <span class="hljs-title function_">groupBy</span>(dailyReports, <span class="hljs-string">&#x27;emp_type&#x27;</span>)
}

<span class="hljs-comment">// 投入成本统计</span>
<span class="hljs-keyword">const</span> costStats = {
  <span class="hljs-attr">totalBudget</span>: <span class="hljs-title function_">sum</span>(devProjects, <span class="hljs-string">&#x27;dev_proj_cost&#x27;</span>),
  <span class="hljs-attr">totalIncome</span>: <span class="hljs-title function_">sum</span>(devProjects, <span class="hljs-string">&#x27;funi_proj_income&#x27;</span>),
  <span class="hljs-attr">totalManMonth</span>: <span class="hljs-title function_">sum</span>(devProjects, <span class="hljs-string">&#x27;dev_proj_imm&#x27;</span>)
}
</code></pre>
<h3 id="2--任务维度管理">2. 📋 任务维度管理</h3>
<p><strong>功能描述</strong>：部门下所有人的任务完成情况、进度、风险监控
<strong>数据计算方式</strong>：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// 任务完成情况统计</span>
<span class="hljs-keyword">const</span> taskStats = {
  <span class="hljs-comment">// 从计划明细表获取任务数据</span>
  <span class="hljs-attr">totalTasks</span>: planDetails.<span class="hljs-property">length</span>,
  <span class="hljs-attr">completedTasks</span>: planDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> p.<span class="hljs-property">status</span> === <span class="hljs-string">&#x27;completed&#x27;</span>).<span class="hljs-property">length</span>,
  <span class="hljs-attr">inProgressTasks</span>: planDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> p.<span class="hljs-property">status</span> === <span class="hljs-string">&#x27;in_progress&#x27;</span>).<span class="hljs-property">length</span>,
  <span class="hljs-attr">delayedTasks</span>: planDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> 
    <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>(p.<span class="hljs-property">plan_end_date</span>) &lt; <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>() &amp;&amp; p.<span class="hljs-property">status</span> !== <span class="hljs-string">&#x27;completed&#x27;</span>
  ).<span class="hljs-property">length</span>,
  
  <span class="hljs-comment">// 进度计算</span>
  <span class="hljs-attr">avgProgress</span>: <span class="hljs-title function_">average</span>(planDetails, <span class="hljs-string">&#x27;schedule&#x27;</span>),
  
  <span class="hljs-comment">// 风险识别</span>
  <span class="hljs-attr">riskTasks</span>: planDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> {
    <span class="hljs-keyword">const</span> planEndDate = <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>(p.<span class="hljs-property">plan_end_date</span>);
    <span class="hljs-keyword">const</span> today = <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>();
    <span class="hljs-keyword">const</span> daysLeft = (planEndDate - today) / (<span class="hljs-number">1000</span> * <span class="hljs-number">60</span> * <span class="hljs-number">60</span> * <span class="hljs-number">24</span>);
    <span class="hljs-keyword">return</span> daysLeft &lt; <span class="hljs-number">3</span> &amp;&amp; p.<span class="hljs-property">schedule</span> &lt; <span class="hljs-number">80</span>; <span class="hljs-comment">// 3天内到期且进度&lt;80%</span>
  })
}

<span class="hljs-comment">// 按人员分组的任务统计</span>
<span class="hljs-keyword">const</span> tasksByPerson = <span class="hljs-title function_">groupBy</span>(planDetails, <span class="hljs-string">&#x27;creator_name&#x27;</span>).<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">group</span> =&gt;</span> ({
  <span class="hljs-attr">person</span>: group.<span class="hljs-property">key</span>,
  <span class="hljs-attr">totalTasks</span>: group.<span class="hljs-property">items</span>.<span class="hljs-property">length</span>,
  <span class="hljs-attr">completedTasks</span>: group.<span class="hljs-property">items</span>.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">t</span> =&gt;</span> t.<span class="hljs-property">status</span> === <span class="hljs-string">&#x27;completed&#x27;</span>).<span class="hljs-property">length</span>,
  <span class="hljs-attr">avgProgress</span>: <span class="hljs-title function_">average</span>(group.<span class="hljs-property">items</span>, <span class="hljs-string">&#x27;schedule&#x27;</span>),
  <span class="hljs-attr">workload</span>: <span class="hljs-title function_">sum</span>(group.<span class="hljs-property">items</span>, <span class="hljs-string">&#x27;estimate_man_hour&#x27;</span>)
}));
</code></pre>
<h3 id="3--人员维度管理">3. 👥 人员维度管理</h3>
<p><strong>功能描述</strong>：查看人员在哪些项目中的参与情况
<strong>数据计算方式</strong>：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// 人员项目参与情况</span>
<span class="hljs-keyword">const</span> staffProjectMapping = dailyReportDetails.<span class="hljs-title function_">reduce</span>(<span class="hljs-function">(<span class="hljs-params">acc, detail</span>) =&gt;</span> {
  <span class="hljs-keyword">const</span> staffKey = detail.<span class="hljs-property">creator_name</span>;
  <span class="hljs-keyword">if</span> (!acc[staffKey]) {
    acc[staffKey] = {
      <span class="hljs-attr">name</span>: staffKey,
      <span class="hljs-attr">dept</span>: detail.<span class="hljs-property">deputy_dept_name</span>,
      <span class="hljs-attr">projects</span>: <span class="hljs-keyword">new</span> <span class="hljs-title class_">Set</span>(),
      <span class="hljs-attr">totalDaysInvested</span>: <span class="hljs-number">0</span>,
      <span class="hljs-attr">activeProjects</span>: []
    };
  }
  
  acc[staffKey].<span class="hljs-property">projects</span>.<span class="hljs-title function_">add</span>(detail.<span class="hljs-property">dev_proj_name</span>);
  acc[staffKey].<span class="hljs-property">totalDaysInvested</span> += detail.<span class="hljs-property">days_invested</span>;
  
  <span class="hljs-comment">// 记录活跃项目详情</span>
  <span class="hljs-keyword">const</span> existingProject = acc[staffKey].<span class="hljs-property">activeProjects</span>.<span class="hljs-title function_">find</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> 
    p.<span class="hljs-property">projectId</span> === detail.<span class="hljs-property">dev_proj_id</span>
  );
  
  <span class="hljs-keyword">if</span> (existingProject) {
    existingProject.<span class="hljs-property">daysInvested</span> += detail.<span class="hljs-property">days_invested</span>;
  } <span class="hljs-keyword">else</span> {
    acc[staffKey].<span class="hljs-property">activeProjects</span>.<span class="hljs-title function_">push</span>({
      <span class="hljs-attr">projectId</span>: detail.<span class="hljs-property">dev_proj_id</span>,
      <span class="hljs-attr">projectName</span>: detail.<span class="hljs-property">dev_proj_name</span>,
      <span class="hljs-attr">daysInvested</span>: detail.<span class="hljs-property">days_invested</span>,
      <span class="hljs-attr">jobContent</span>: detail.<span class="hljs-property">job_content</span>
    });
  }
  
  <span class="hljs-keyword">return</span> acc;
}, {});
</code></pre>
<h3 id="4--项目维度管理">4. 🎯 项目维度管理</h3>
<p><strong>功能描述</strong>：查看项目中投入的人员清单及投入比例
<strong>数据计算方式</strong>：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// 项目人员投入分析</span>
<span class="hljs-keyword">const</span> projectStaffAnalysis = devProjects.<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">project</span> =&gt;</span> {
  <span class="hljs-comment">// 获取该项目的所有日报详情</span>
  <span class="hljs-keyword">const</span> projectDetails = dailyReportDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">d</span> =&gt;</span> 
    d.<span class="hljs-property">dev_proj_id</span> === project.<span class="hljs-property">id</span>
  );
  
  <span class="hljs-comment">// 按人员分组统计投入</span>
  <span class="hljs-keyword">const</span> staffInvestment = <span class="hljs-title function_">groupBy</span>(projectDetails, <span class="hljs-string">&#x27;creator_name&#x27;</span>).<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">group</span> =&gt;</span> {
    <span class="hljs-keyword">const</span> totalDays = <span class="hljs-title function_">sum</span>(group.<span class="hljs-property">items</span>, <span class="hljs-string">&#x27;days_invested&#x27;</span>);
    
    <span class="hljs-comment">// 计算该人员在所有项目的总投入</span>
    <span class="hljs-keyword">const</span> personAllProjects = dailyReportDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">d</span> =&gt;</span> 
      d.<span class="hljs-property">creator_name</span> === group.<span class="hljs-property">key</span>
    );
    <span class="hljs-keyword">const</span> personTotalDays = <span class="hljs-title function_">sum</span>(personAllProjects, <span class="hljs-string">&#x27;days_invested&#x27;</span>);
    
    <span class="hljs-keyword">return</span> {
      <span class="hljs-attr">name</span>: group.<span class="hljs-property">key</span>,
      <span class="hljs-attr">dept</span>: group.<span class="hljs-property">items</span>[<span class="hljs-number">0</span>].<span class="hljs-property">deputy_dept_name</span>,
      <span class="hljs-attr">daysInProject</span>: totalDays,
      <span class="hljs-attr">totalDaysAllProjects</span>: personTotalDays,
      <span class="hljs-attr">investmentRatio</span>: (totalDays / personTotalDays * <span class="hljs-number">100</span>).<span class="hljs-title function_">toFixed</span>(<span class="hljs-number">2</span>) + <span class="hljs-string">&#x27;%&#x27;</span>,
      <span class="hljs-attr">recentActivities</span>: group.<span class="hljs-property">items</span>.<span class="hljs-title function_">slice</span>(-<span class="hljs-number">5</span>) <span class="hljs-comment">// 最近5条工作记录</span>
    };
  });
  
  <span class="hljs-keyword">return</span> {
    <span class="hljs-attr">projectId</span>: project.<span class="hljs-property">id</span>,
    <span class="hljs-attr">projectName</span>: project.<span class="hljs-property">dev_proj_name</span>,
    <span class="hljs-attr">projectManager</span>: project.<span class="hljs-property">dev_proj_pm</span>,
    <span class="hljs-attr">totalStaff</span>: staffInvestment.<span class="hljs-property">length</span>,
    <span class="hljs-attr">totalDaysInvested</span>: <span class="hljs-title function_">sum</span>(staffInvestment, <span class="hljs-string">&#x27;daysInProject&#x27;</span>),
    <span class="hljs-attr">staffDetails</span>: staffInvestment,
    <span class="hljs-attr">projectStatus</span>: project.<span class="hljs-property">dev_proj_status</span>,
    <span class="hljs-attr">budget</span>: project.<span class="hljs-property">dev_proj_cost</span>,
    <span class="hljs-attr">expectedIncome</span>: project.<span class="hljs-property">funi_proj_income</span>
  };
});
</code></pre>
<h3 id="5-️-人员工作饱和度分析">5. ⚖️ 人员工作饱和度分析</h3>
<p><strong>功能描述</strong>：查看人员工作饱和度，识别过载情况
<strong>数据计算方式</strong>：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// 人员工作饱和度计算</span>
<span class="hljs-keyword">const</span> staffWorkloadAnalysis = <span class="hljs-title class_">Object</span>.<span class="hljs-title function_">values</span>(staffProjectMapping).<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">staff</span> =&gt;</span> {
  <span class="hljs-comment">// 计算月度工作饱和度（假设每月22个工作日）</span>
  <span class="hljs-keyword">const</span> monthlyWorkDays = <span class="hljs-number">22</span>;
  <span class="hljs-keyword">const</span> currentMonth = <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>().<span class="hljs-title function_">getMonth</span>();
  
  <span class="hljs-comment">// 获取当月的工作投入</span>
  <span class="hljs-keyword">const</span> currentMonthDetails = dailyReportDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">d</span> =&gt;</span> 
    d.<span class="hljs-property">creator_name</span> === staff.<span class="hljs-property">name</span> &amp;&amp; 
    <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>(d.<span class="hljs-property">create_time</span>).<span class="hljs-title function_">getMonth</span>() === currentMonth
  );
  
  <span class="hljs-keyword">const</span> monthlyDaysInvested = <span class="hljs-title function_">sum</span>(currentMonthDetails, <span class="hljs-string">&#x27;days_invested&#x27;</span>);
  <span class="hljs-keyword">const</span> saturationRate = (monthlyDaysInvested / monthlyWorkDays * <span class="hljs-number">100</span>).<span class="hljs-title function_">toFixed</span>(<span class="hljs-number">2</span>);
  
  <span class="hljs-comment">// 获取计划工时</span>
  <span class="hljs-keyword">const</span> staffPlans = planDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> p.<span class="hljs-property">creator_name</span> === staff.<span class="hljs-property">name</span>);
  <span class="hljs-keyword">const</span> plannedHours = <span class="hljs-title function_">sum</span>(staffPlans, <span class="hljs-string">&#x27;estimate_man_hour&#x27;</span>);
  <span class="hljs-keyword">const</span> actualHours = <span class="hljs-title function_">sum</span>(staffPlans, <span class="hljs-string">&#x27;actual_man_hour&#x27;</span>);
  
  <span class="hljs-keyword">return</span> {
    <span class="hljs-attr">name</span>: staff.<span class="hljs-property">name</span>,
    <span class="hljs-attr">dept</span>: staff.<span class="hljs-property">dept</span>,
    monthlyDaysInvested,
    <span class="hljs-attr">saturationRate</span>: <span class="hljs-built_in">parseFloat</span>(saturationRate),
    <span class="hljs-attr">isOverloaded</span>: <span class="hljs-built_in">parseFloat</span>(saturationRate) &gt; <span class="hljs-number">100</span>,
    plannedHours,
    actualHours,
    <span class="hljs-attr">efficiency</span>: actualHours &gt; <span class="hljs-number">0</span> ? (plannedHours / actualHours * <span class="hljs-number">100</span>).<span class="hljs-title function_">toFixed</span>(<span class="hljs-number">2</span>) + <span class="hljs-string">&#x27;%&#x27;</span> : <span class="hljs-string">&#x27;N/A&#x27;</span>,
    <span class="hljs-attr">projectCount</span>: staff.<span class="hljs-property">projects</span>.<span class="hljs-property">size</span>,
    <span class="hljs-attr">riskLevel</span>: <span class="hljs-built_in">parseFloat</span>(saturationRate) &gt; <span class="hljs-number">120</span> ? <span class="hljs-string">&#x27;high&#x27;</span> : 
               <span class="hljs-built_in">parseFloat</span>(saturationRate) &gt; <span class="hljs-number">100</span> ? <span class="hljs-string">&#x27;medium&#x27;</span> : <span class="hljs-string">&#x27;low&#x27;</span>
  };
});
</code></pre>
<h3 id="6--人员忙闲状态日历">6. 📅 人员忙闲状态日历</h3>
<p><strong>功能描述</strong>：查看人员在时间跨度中的具体忙闲状态
<strong>数据计算方式</strong>：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// 人员日程状态分析</span>
<span class="hljs-keyword">const</span> <span class="hljs-title function_">generateStaffCalendar</span> = (<span class="hljs-params">staffName, startDate, endDate</span>) =&gt; {
  <span class="hljs-keyword">const</span> calendar = [];
  <span class="hljs-keyword">const</span> start = <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>(startDate);
  <span class="hljs-keyword">const</span> end = <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>(endDate);
  
  <span class="hljs-keyword">for</span> (<span class="hljs-keyword">let</span> date = <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>(start); date &lt;= end; date.<span class="hljs-title function_">setDate</span>(date.<span class="hljs-title function_">getDate</span>() + <span class="hljs-number">1</span>)) {
    <span class="hljs-keyword">const</span> dateStr = date.<span class="hljs-title function_">toISOString</span>().<span class="hljs-title function_">split</span>(<span class="hljs-string">&#x27;T&#x27;</span>)[<span class="hljs-number">0</span>];
    
    <span class="hljs-comment">// 检查该日期的日报</span>
    <span class="hljs-keyword">const</span> dailyReport = dailyReports.<span class="hljs-title function_">find</span>(<span class="hljs-function"><span class="hljs-params">r</span> =&gt;</span> 
      r.<span class="hljs-property">creator_name</span> === staffName &amp;&amp; 
      r.<span class="hljs-property">daily_date</span> === dateStr
    );
    
    <span class="hljs-comment">// 检查该日期的计划任务</span>
    <span class="hljs-keyword">const</span> dayTasks = planDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> 
      p.<span class="hljs-property">creator_name</span> === staffName &amp;&amp;
      dateStr &gt;= p.<span class="hljs-property">plan_begin_date</span> &amp;&amp; 
      dateStr &lt;= p.<span class="hljs-property">plan_end_date</span>
    );
    
    calendar.<span class="hljs-title function_">push</span>({
      <span class="hljs-attr">date</span>: dateStr,
      <span class="hljs-attr">hasReport</span>: !!dailyReport,
      <span class="hljs-attr">hasTasks</span>: dayTasks.<span class="hljs-property">length</span> &gt; <span class="hljs-number">0</span>,
      <span class="hljs-attr">taskCount</span>: dayTasks.<span class="hljs-property">length</span>,
      <span class="hljs-attr">completedTasks</span>: dayTasks.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">t</span> =&gt;</span> t.<span class="hljs-property">status</span> === <span class="hljs-string">&#x27;completed&#x27;</span>).<span class="hljs-property">length</span>,
      <span class="hljs-attr">workload</span>: <span class="hljs-title function_">sum</span>(dayTasks, <span class="hljs-string">&#x27;estimate_man_hour&#x27;</span>),
      <span class="hljs-attr">status</span>: dailyReport ? <span class="hljs-string">&#x27;busy&#x27;</span> : (dayTasks.<span class="hljs-property">length</span> &gt; <span class="hljs-number">0</span> ? <span class="hljs-string">&#x27;scheduled&#x27;</span> : <span class="hljs-string">&#x27;free&#x27;</span>),
      <span class="hljs-attr">activities</span>: dailyReport ? [dailyReport.<span class="hljs-property">daily_titile</span>] : [],
      <span class="hljs-attr">tasks</span>: dayTasks.<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">t</span> =&gt;</span> ({
        <span class="hljs-attr">content</span>: t.<span class="hljs-property">job_content</span>,
        <span class="hljs-attr">status</span>: t.<span class="hljs-property">status</span>,
        <span class="hljs-attr">progress</span>: t.<span class="hljs-property">schedule</span>
      }))
    });
  }
  
  <span class="hljs-keyword">return</span> calendar;
};
</code></pre>
<h3 id="7--项目进度监控">7. 📈 项目进度监控</h3>
<p><strong>功能描述</strong>：项目进度跟踪和里程碑管理
<strong>数据计算方式</strong>：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// 项目进度分析</span>
<span class="hljs-keyword">const</span> projectProgressAnalysis = devProjects.<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">project</span> =&gt;</span> {
  <span class="hljs-keyword">const</span> projectPlans = planDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> p.<span class="hljs-property">dev_proj_id</span> === project.<span class="hljs-property">id</span>);
  <span class="hljs-keyword">const</span> projectReports = dailyReportDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">d</span> =&gt;</span> d.<span class="hljs-property">dev_proj_id</span> === project.<span class="hljs-property">id</span>);
  
  <span class="hljs-comment">// 计算项目整体进度</span>
  <span class="hljs-keyword">const</span> avgProgress = projectPlans.<span class="hljs-property">length</span> &gt; <span class="hljs-number">0</span> ? 
    <span class="hljs-title function_">average</span>(projectPlans, <span class="hljs-string">&#x27;schedule&#x27;</span>) : <span class="hljs-number">0</span>;
  
  <span class="hljs-comment">// 计算时间进度</span>
  <span class="hljs-keyword">const</span> startDate = <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>(project.<span class="hljs-property">funi_proj_begin_date</span>);
  <span class="hljs-keyword">const</span> endDate = <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>(project.<span class="hljs-property">funi_proj_end_date</span>);
  <span class="hljs-keyword">const</span> today = <span class="hljs-keyword">new</span> <span class="hljs-title class_">Date</span>();
  <span class="hljs-keyword">const</span> totalDuration = endDate - startDate;
  <span class="hljs-keyword">const</span> elapsedDuration = today - startDate;
  <span class="hljs-keyword">const</span> timeProgress = (elapsedDuration / totalDuration * <span class="hljs-number">100</span>).<span class="hljs-title function_">toFixed</span>(<span class="hljs-number">2</span>);
  
  <span class="hljs-comment">// 风险评估</span>
  <span class="hljs-keyword">const</span> scheduleRisk = avgProgress &lt; <span class="hljs-built_in">parseFloat</span>(timeProgress) - <span class="hljs-number">10</span>;
  
  <span class="hljs-keyword">return</span> {
    <span class="hljs-attr">projectId</span>: project.<span class="hljs-property">id</span>,
    <span class="hljs-attr">projectName</span>: project.<span class="hljs-property">dev_proj_name</span>,
    <span class="hljs-attr">workProgress</span>: avgProgress,
    <span class="hljs-attr">timeProgress</span>: <span class="hljs-built_in">parseFloat</span>(timeProgress),
    <span class="hljs-attr">isDelayed</span>: scheduleRisk,
    <span class="hljs-attr">totalTasks</span>: projectPlans.<span class="hljs-property">length</span>,
    <span class="hljs-attr">completedTasks</span>: projectPlans.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> p.<span class="hljs-property">status</span> === <span class="hljs-string">&#x27;completed&#x27;</span>).<span class="hljs-property">length</span>,
    <span class="hljs-attr">activeDays</span>: projectReports.<span class="hljs-property">length</span>,
    <span class="hljs-attr">totalInvestment</span>: <span class="hljs-title function_">sum</span>(projectReports, <span class="hljs-string">&#x27;days_invested&#x27;</span>),
    <span class="hljs-attr">riskLevel</span>: scheduleRisk ? <span class="hljs-string">&#x27;high&#x27;</span> : (avgProgress &lt; <span class="hljs-number">50</span> ? <span class="hljs-string">&#x27;medium&#x27;</span> : <span class="hljs-string">&#x27;low&#x27;</span>)
  };
});
</code></pre>
<h3 id="8--成本效益分析">8. 💰 成本效益分析</h3>
<p><strong>功能描述</strong>：项目成本投入与收益分析
<strong>数据计算方式</strong>：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// 成本效益分析</span>
<span class="hljs-keyword">const</span> costBenefitAnalysis = devProjects.<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">project</span> =&gt;</span> {
  <span class="hljs-keyword">const</span> projectReports = dailyReportDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">d</span> =&gt;</span> d.<span class="hljs-property">dev_proj_id</span> === project.<span class="hljs-property">id</span>);
  <span class="hljs-keyword">const</span> actualInvestment = <span class="hljs-title function_">sum</span>(projectReports, <span class="hljs-string">&#x27;days_invested&#x27;</span>);

  <span class="hljs-comment">// 假设日均成本（可配置）</span>
  <span class="hljs-keyword">const</span> dailyCost = <span class="hljs-number">800</span>; <span class="hljs-comment">// 元/人天</span>
  <span class="hljs-keyword">const</span> actualCost = actualInvestment * dailyCost;

  <span class="hljs-keyword">return</span> {
    <span class="hljs-attr">projectName</span>: project.<span class="hljs-property">dev_proj_name</span>,
    <span class="hljs-attr">budgetCost</span>: project.<span class="hljs-property">dev_proj_cost</span> || <span class="hljs-number">0</span>,
    actualCost,
    <span class="hljs-attr">expectedIncome</span>: project.<span class="hljs-property">funi_proj_income</span> || <span class="hljs-number">0</span>,
    <span class="hljs-attr">costVariance</span>: ((actualCost - project.<span class="hljs-property">dev_proj_cost</span>) / project.<span class="hljs-property">dev_proj_cost</span> * <span class="hljs-number">100</span>).<span class="hljs-title function_">toFixed</span>(<span class="hljs-number">2</span>) + <span class="hljs-string">&#x27;%&#x27;</span>,
    <span class="hljs-attr">roi</span>: project.<span class="hljs-property">funi_proj_income</span> &gt; <span class="hljs-number">0</span> ?
      ((project.<span class="hljs-property">funi_proj_income</span> - actualCost) / actualCost * <span class="hljs-number">100</span>).<span class="hljs-title function_">toFixed</span>(<span class="hljs-number">2</span>) + <span class="hljs-string">&#x27;%&#x27;</span> : <span class="hljs-string">&#x27;N/A&#x27;</span>,
    <span class="hljs-attr">profitMargin</span>: project.<span class="hljs-property">funi_proj_income</span> &gt; <span class="hljs-number">0</span> ?
      ((project.<span class="hljs-property">funi_proj_income</span> - actualCost) / project.<span class="hljs-property">funi_proj_income</span> * <span class="hljs-number">100</span>).<span class="hljs-title function_">toFixed</span>(<span class="hljs-number">2</span>) + <span class="hljs-string">&#x27;%&#x27;</span> : <span class="hljs-string">&#x27;N/A&#x27;</span>
  };
});
</code></pre>
<h3 id="9--部门资源配置">9. 🏢 部门资源配置</h3>
<p><strong>功能描述</strong>：部门间资源分配和协调
<strong>数据计算方式</strong>：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// 部门资源配置分析</span>
<span class="hljs-keyword">const</span> deptResourceAllocation = <span class="hljs-title function_">groupBy</span>(dailyReports, <span class="hljs-string">&#x27;first_dept_name&#x27;</span>).<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">dept</span> =&gt;</span> {
  <span class="hljs-keyword">const</span> deptStaff = dept.<span class="hljs-property">items</span>;
  <span class="hljs-keyword">const</span> deptProjects = [...<span class="hljs-keyword">new</span> <span class="hljs-title class_">Set</span>(
    dailyReportDetails
      .<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">d</span> =&gt;</span> deptStaff.<span class="hljs-title function_">some</span>(<span class="hljs-function"><span class="hljs-params">s</span> =&gt;</span> s.<span class="hljs-property">creator_name</span> === d.<span class="hljs-property">creator_name</span>))
      .<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">d</span> =&gt;</span> d.<span class="hljs-property">dev_proj_id</span>)
  )];

  <span class="hljs-keyword">return</span> {
    <span class="hljs-attr">deptName</span>: dept.<span class="hljs-property">key</span>,
    <span class="hljs-attr">staffCount</span>: deptStaff.<span class="hljs-property">length</span>,
    <span class="hljs-attr">projectCount</span>: deptProjects.<span class="hljs-property">length</span>,
    <span class="hljs-attr">totalWorkload</span>: <span class="hljs-title function_">sum</span>(deptStaff.<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">s</span> =&gt;</span>
      planDetails.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">p</span> =&gt;</span> p.<span class="hljs-property">creator_name</span> === s.<span class="hljs-property">creator_name</span>)
    ).<span class="hljs-title function_">flat</span>(), <span class="hljs-string">&#x27;estimate_man_hour&#x27;</span>),
    <span class="hljs-attr">avgSaturation</span>: <span class="hljs-title function_">average</span>(
      staffWorkloadAnalysis.<span class="hljs-title function_">filter</span>(<span class="hljs-function"><span class="hljs-params">s</span> =&gt;</span>
        deptStaff.<span class="hljs-title function_">some</span>(<span class="hljs-function"><span class="hljs-params">ds</span> =&gt;</span> ds.<span class="hljs-property">creator_name</span> === s.<span class="hljs-property">name</span>)
      ),
      <span class="hljs-string">&#x27;saturationRate&#x27;</span>
    )
  };
});
</code></pre>
<h3 id="10--数据导出与报表">10. 📊 数据导出与报表</h3>
<p><strong>功能描述</strong>：生成各类管理报表
<strong>数据计算方式</strong>：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// 报表数据生成</span>
<span class="hljs-keyword">const</span> generateReports = {
  <span class="hljs-attr">weekly</span>: <span class="hljs-function">() =&gt;</span> <span class="hljs-title function_">generateWeeklyReport</span>(staffWorkloadAnalysis, projectProgressAnalysis),
  <span class="hljs-attr">monthly</span>: <span class="hljs-function">() =&gt;</span> <span class="hljs-title function_">generateMonthlyReport</span>(costBenefitAnalysis, deptResourceAllocation),
  <span class="hljs-attr">quarterly</span>: <span class="hljs-function">() =&gt;</span> <span class="hljs-title function_">generateQuarterlyReport</span>(projectStats, staffStats),
  <span class="hljs-attr">custom</span>: <span class="hljs-function">(<span class="hljs-params">startDate, endDate</span>) =&gt;</span> <span class="hljs-title function_">generateCustomReport</span>(startDate, endDate)
};
</code></pre>
<h2 id="数据关联关系图">数据关联关系图</h2>
<pre><code>日报表 ←→ 日报详情表 (daily_info)
    ↓
员工信息 ←→ 计划管理 ←→ 计划明细
    ↓           ↓
研发项目 ←→ 项目产品关系 ←→ 产品信息
    ↓
区域信息
</code></pre>
<h2 id="工具函数说明">工具函数说明</h2>
<h3 id="数据聚合函数">数据聚合函数</h3>
<pre><code class="language-javascript"><span class="hljs-comment">// 分组函数</span>
<span class="hljs-keyword">const</span> <span class="hljs-title function_">groupBy</span> = (<span class="hljs-params">array, key</span>) =&gt; {
  <span class="hljs-keyword">return</span> array.<span class="hljs-title function_">reduce</span>(<span class="hljs-function">(<span class="hljs-params">groups, item</span>) =&gt;</span> {
    <span class="hljs-keyword">const</span> group = item[key];
    <span class="hljs-keyword">if</span> (!groups[group]) {
      groups[group] = { <span class="hljs-attr">key</span>: group, <span class="hljs-attr">items</span>: [] };
    }
    groups[group].<span class="hljs-property">items</span>.<span class="hljs-title function_">push</span>(item);
    <span class="hljs-keyword">return</span> groups;
  }, {});
};

<span class="hljs-comment">// 求和函数</span>
<span class="hljs-keyword">const</span> <span class="hljs-title function_">sum</span> = (<span class="hljs-params">array, key</span>) =&gt; {
  <span class="hljs-keyword">return</span> array.<span class="hljs-title function_">reduce</span>(<span class="hljs-function">(<span class="hljs-params">total, item</span>) =&gt;</span> total + (item[key] || <span class="hljs-number">0</span>), <span class="hljs-number">0</span>);
};

<span class="hljs-comment">// 平均值函数</span>
<span class="hljs-keyword">const</span> <span class="hljs-title function_">average</span> = (<span class="hljs-params">array, key</span>) =&gt; {
  <span class="hljs-keyword">if</span> (array.<span class="hljs-property">length</span> === <span class="hljs-number">0</span>) <span class="hljs-keyword">return</span> <span class="hljs-number">0</span>;
  <span class="hljs-keyword">return</span> <span class="hljs-title function_">sum</span>(array, key) / array.<span class="hljs-property">length</span>;
};

<span class="hljs-comment">// 去重函数</span>
<span class="hljs-keyword">const</span> <span class="hljs-title function_">unique</span> = (<span class="hljs-params">array, key</span>) =&gt; {
  <span class="hljs-keyword">return</span> array.<span class="hljs-title function_">filter</span>(<span class="hljs-function">(<span class="hljs-params">item, index, self</span>) =&gt;</span>
    index === self.<span class="hljs-title function_">findIndex</span>(<span class="hljs-function"><span class="hljs-params">t</span> =&gt;</span> t[key] === item[key])
  );
};
</code></pre>
<h2 id="技术实现要点">技术实现要点</h2>
<h3 id="1-数据获取策略">1. 数据获取策略</h3>
<ul>
<li>使用现有API接口获取基础数据</li>
<li>前端进行数据聚合和计算</li>
<li>实现数据缓存机制提升性能</li>
</ul>
<h3 id="2-实时更新机制">2. 实时更新机制</h3>
<ul>
<li>定时刷新关键数据</li>
<li>支持手动刷新功能</li>
<li>数据变更提醒</li>
</ul>
<h3 id="3-性能优化">3. 性能优化</h3>
<ul>
<li>分页加载大数据集</li>
<li>虚拟滚动处理长列表</li>
<li>图表数据采样显示</li>
</ul>
<h3 id="4-用户体验">4. 用户体验</h3>
<ul>
<li>响应式设计适配不同屏幕</li>
<li>交互式图表支持钻取</li>
<li>导出功能支持多种格式</li>
</ul>
<h2 id="部署说明">部署说明</h2>
<h3 id="文件结构">文件结构</h3>
<pre><code>dashboard/
├── index.html          # 主页面
├── css/
│   └── style.css      # 样式文件
├── js/
│   ├── main.js        # 主逻辑
│   ├── api.js         # API接口
│   ├── utils.js       # 工具函数
│   └── charts.js      # 图表配置
└── assets/            # 静态资源
</code></pre>
<h3 id="环境要求">环境要求</h3>
<ul>
<li>现代浏览器支持ES6+</li>
<li>网络访问API接口</li>
<li>无需服务器端部署</li>
</ul>
<p>这个功能清单涵盖了研发中心负责人需要的核心管理功能，通过数据聚合和计算提供全面的决策支持信息。</p>

            
            
        </body>
        </html>