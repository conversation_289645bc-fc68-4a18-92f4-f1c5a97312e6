#!/bin/bash

echo "🚀 启动投入分析系统..."
echo "================================"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查Python是否可用
if command -v python3 &> /dev/null; then
    echo "✅ 找到Python3，启动HTTP服务器..."
    
    # 启动Python HTTP服务器
    python3 -m http.server 8080 &
    SERVER_PID=$!
    
    echo "📡 服务器PID: $SERVER_PID"
    echo "⏳ 等待服务器启动..."
    sleep 3
    
    # 显示访问地址
    echo ""
    echo "🌐 服务器已启动，访问地址："
    echo "   人员分析: http://localhost:8080/person-analysis.html"
    echo "   项目分析: http://localhost:8080/project-analysis.html"
    echo "   部门分析: http://localhost:8080/department-analysis.html"
    echo "   API测试:  http://localhost:8080/api-test.html"
    echo ""
    
    # 尝试自动打开浏览器
    if command -v open &> /dev/null; then
        echo "🔗 自动打开浏览器..."
        open http://localhost:8080/person-analysis.html
    elif command -v xdg-open &> /dev/null; then
        echo "🔗 自动打开浏览器..."
        xdg-open http://localhost:8080/person-analysis.html
    else
        echo "💡 请手动在浏览器中打开: http://localhost:8080/person-analysis.html"
    fi
    
    echo ""
    echo "🛑 按 Ctrl+C 停止服务器"
    echo "================================"
    
    # 设置信号处理，优雅关闭服务器
    trap "echo ''; echo '🛑 正在停止服务器...'; kill $SERVER_PID 2>/dev/null; echo '✅ 服务器已停止'; exit 0" INT TERM
    
    # 等待服务器进程
    wait $SERVER_PID
    
elif command -v python &> /dev/null; then
    echo "✅ 找到Python2，启动HTTP服务器..."
    
    # 启动Python2 HTTP服务器
    python -m SimpleHTTPServer 8080 &
    SERVER_PID=$!
    
    echo "📡 服务器PID: $SERVER_PID"
    echo "⏳ 等待服务器启动..."
    sleep 3
    
    echo ""
    echo "🌐 服务器已启动，访问地址："
    echo "   人员分析: http://localhost:8080/person-analysis.html"
    echo "   项目分析: http://localhost:8080/project-analysis.html"
    echo "   部门分析: http://localhost:8080/department-analysis.html"
    echo "   API测试:  http://localhost:8080/api-test.html"
    echo ""
    
    # 尝试自动打开浏览器
    if command -v open &> /dev/null; then
        echo "🔗 自动打开浏览器..."
        open http://localhost:8080/person-analysis.html
    elif command -v xdg-open &> /dev/null; then
        echo "🔗 自动打开浏览器..."
        xdg-open http://localhost:8080/person-analysis.html
    else
        echo "💡 请手动在浏览器中打开: http://localhost:8080/person-analysis.html"
    fi
    
    echo ""
    echo "🛑 按 Ctrl+C 停止服务器"
    echo "================================"
    
    # 设置信号处理
    trap "echo ''; echo '🛑 正在停止服务器...'; kill $SERVER_PID 2>/dev/null; echo '✅ 服务器已停止'; exit 0" INT TERM
    
    # 等待服务器进程
    wait $SERVER_PID
    
else
    echo "❌ 错误: 未找到Python"
    echo ""
    echo "请安装Python后重试，或使用其他方法："
    echo "1. 安装Python: https://www.python.org/downloads/"
    echo "2. 使用Node.js: npm install -g http-server && http-server -p 8080"
    echo "3. 使用VS Code Live Server插件"
    echo ""
    exit 1
fi
