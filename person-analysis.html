<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投入分析系统 - 人员维度</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #303133;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .filter-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 40% 60%;
            gap: 20px;
            min-height: 600px;
        }
        
        .left-panel, .right-panel {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #409EFF;
        }
        
        .employee-table {
            width: 100%;
        }
        
        .chart-container {
            margin-bottom: 30px;
        }
        
        .chart-box {
            height: 300px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
        }

        .pagination-container {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 20px 0;
            border-top: 1px solid #e4e7ed;
            margin-top: 10px;
            background: #fafafa;
            border-radius: 0 0 8px 8px;
        }

        /* 优化Element Plus分页组件样式 */
        .pagination-container .el-pagination {
            --el-pagination-font-size: 14px;
            --el-pagination-bg-color: transparent;
            --el-pagination-text-color: #606266;
            --el-pagination-border-radius: 6px;
            --el-pagination-button-color: #606266;
            --el-pagination-button-bg-color: #fff;
            --el-pagination-button-disabled-color: #c0c4cc;
            --el-pagination-button-disabled-bg-color: #fff;
            --el-pagination-hover-color: #409eff;
            --el-pagination-active-color: #409eff;
            --el-pagination-active-bg-color: #409eff;
        }

        .pagination-container .el-pagination .el-pager li {
            min-width: 32px;
            height: 32px;
            line-height: 30px;
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            margin: 0 2px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .pagination-container .el-pagination .el-pager li:hover {
            border-color: #409eff;
            color: #409eff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        .pagination-container .el-pagination .el-pager li.is-active {
            background: #409eff;
            border-color: #409eff;
            color: #fff;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }

        .pagination-container .el-pagination .btn-prev,
        .pagination-container .el-pagination .btn-next {
            min-width: 32px;
            height: 32px;
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            margin: 0 2px;
            transition: all 0.2s ease;
        }

        .pagination-container .el-pagination .btn-prev:hover,
        .pagination-container .el-pagination .btn-next:hover {
            border-color: #409eff;
            color: #409eff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        .pagination-container .el-pagination .el-select .el-input {
            width: 80px;
        }

        .pagination-container .el-pagination .el-select .el-input__wrapper {
            border-radius: 6px;
            border: 1px solid #dcdfe6;
            transition: all 0.2s ease;
        }

        .pagination-container .el-pagination .el-select .el-input__wrapper:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        .pagination-container .el-pagination .el-pagination__jump {
            margin-left: 16px;
        }

        .pagination-container .el-pagination .el-pagination__jump .el-input {
            width: 60px;
        }

        .pagination-container .el-pagination .el-pagination__jump .el-input__wrapper {
            border-radius: 6px;
            border: 1px solid #dcdfe6;
            transition: all 0.2s ease;
        }

        .pagination-container .el-pagination .el-pagination__jump .el-input__wrapper:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        .pagination-container .el-pagination .el-pagination__total {
            color: #909399;
            font-weight: 500;
            margin-right: 16px;
        }

        .pagination-container .el-pagination .el-pagination__sizes {
            margin-right: 16px;
        }
        
        .efficiency-bar {
            width: 100%;
            margin-top: 5px;
        }
        
        .project-detail-table {
            margin-top: 20px;
        }
        
        .tag-success {
            background-color: #f0f9ff;
            color: #1890ff;
            border: 1px solid #d1ecf1;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .tag-primary {
            background-color: #ecf5ff;
            color: #409eff;
            border: 1px solid #b3d8ff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .badge {
            background-color: #67c23a;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 12px;
            min-width: 18px;
            text-align: center;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #e4e7ed;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #409eff;
            transition: width 0.3s ease;
        }
        
        .selected-row {
            background-color: #ecf5ff !important;
        }
        
        .employee-row {
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .employee-row:hover {
            background-color: #f5f7fa;
        }
        
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        .data-indicator {
            display: inline-flex;
            align-items: center;
            margin-left: 8px;
            font-size: 14px;
        }

        .data-real {
            color: #67c23a;
        }

        .data-mock {
            color: #e6a23c;
        }

        .indicator-tooltip {
            margin-left: 4px;
            cursor: help;
        }

        /* 数据标识图标样式 */
        .data-indicator .el-icon-success::before {
            content: "✓";
            font-style: normal;
            font-weight: bold;
        }

        .data-indicator .el-icon-warning::before {
            content: "⚠️";
            font-style: normal;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 15px 0;
            }

            .header h1 {
                font-size: 22px;
                margin-bottom: 8px;
            }

            .header p {
                font-size: 13px;
            }

            .filter-section {
                margin-bottom: 20px;
            }

            .filter-section .el-row {
                flex-direction: column;
                gap: 10px;
            }

            .filter-section .el-col {
                width: 100% !important;
                max-width: none !important;
            }

            .main-content {
                flex-direction: column;
                gap: 20px;
            }

            .left-panel,
            .right-panel {
                width: 100%;
                min-width: auto;
            }

            .panel-title {
                font-size: 16px;
                margin-bottom: 15px;
            }

            .employee-table {
                font-size: 12px;
                overflow-x: auto;
                display: block;
                white-space: nowrap;
                -webkit-overflow-scrolling: touch;
            }

            .employee-table th,
            .employee-table td {
                padding: 8px 4px !important;
                min-width: 80px;
                font-size: 11px;
            }

            .employee-table th {
                background-color: #f8f9fa;
                position: sticky;
                top: 0;
                z-index: 10;
            }

            .chart-container {
                height: 250px;
                margin-bottom: 20px;
            }

            .chart-container h4 {
                font-size: 14px;
                margin-bottom: 10px;
            }

            .chart-box {
                height: 200px;
            }

            .pagination-container {
                justify-content: center;
                margin-top: 15px;
            }

            .project-detail-table {
                margin-top: 20px;
            }

            .project-detail-table h4 {
                font-size: 14px;
                margin-bottom: 10px;
            }

            .project-detail-table table {
                font-size: 12px;
                overflow-x: auto;
                display: block;
                -webkit-overflow-scrolling: touch;
            }

            .project-detail-table th,
            .project-detail-table td {
                padding: 6px 4px !important;
                min-width: 80px;
                font-size: 11px;
                white-space: nowrap;
            }

            .project-detail-table th {
                background-color: #f8f9fa;
                position: sticky;
                top: 0;
                z-index: 10;
            }

            .el-pagination {
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 18px;
            }

            .header p {
                font-size: 12px;
            }

            .chart-container {
                height: 200px;
            }

            .chart-box {
                height: 150px;
            }

            .employee-table th,
            .employee-table td {
                padding: 6px 2px !important;
                font-size: 11px;
            }

            .project-detail-table th,
            .project-detail-table td {
                padding: 4px 2px !important;
                font-size: 11px;
            }

            .el-pagination {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 页面头部 -->
            <div class="header">
                <h1>投入分析系统 - 人员维度</h1>
                <p>查看每个人投入到哪些项目上和在每个项目的投入比例</p>
            </div>
            
            <!-- 筛选条件 -->
            <div class="filter-section">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-date-picker
                            v-model="filterForm.currentMonth"
                            type="month"
                            placeholder="选择月份"
                            style="width: 100%"
                            format="YYYY年MM月"
                            value-format="YYYY-MM">
                        </el-date-picker>
                    </el-col>
                    <el-col :span="6">
                        <el-select v-model="filterForm.department" placeholder="选择部门" style="width: 100%">
                            <el-option label="全部部门" value=""></el-option>
                            <el-option
                                v-for="dept in departmentOptions"
                                :key="dept.value"
                                :label="dept.label"
                                :value="dept.value">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <el-select v-model="filterForm.empType" placeholder="员工类型" style="width: 100%">
                            <el-option label="全部类型" value=""></el-option>
                            <el-option
                                v-for="type in empTypeOptions"
                                :key="type.value"
                                :label="type.label"
                                :value="type.value">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <el-input
                            v-model="filterForm.search"
                            placeholder="搜索员工姓名或工号"
                            clearable>
                            <template #prefix>
                                <el-icon><i class="el-icon-search"></i></el-icon>
                            </template>
                        </el-input>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 15px;">
                    <el-button type="primary" @click="applyFilter">应用筛选</el-button>
                    <el-button @click="resetFilter">重置</el-button>
                </el-row>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 左侧人员列表 -->
                <div class="left-panel">
                    <div class="panel-title">
                        人员列表
                        <span class="data-indicator data-real" title="真实数据：来自API接口">
                            <i class="el-icon-success"></i>
                        </span>
                    </div>
                    <table class="employee-table">
                        <thead>
                            <tr style="background-color: #fafafa;">
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e4e7ed;">姓名</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e4e7ed;">工号</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e4e7ed;">部门</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e4e7ed;">总投入天数</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e4e7ed;">项目数量</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e4e7ed;">
                                    效率指标
                                    <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                                        <i class="el-icon-warning"></i>
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(employee, index) in paginatedEmployees"
                                :key="employee.emp_no"
                                :class="['employee-row', { 'selected-row': selectedEmployee?.emp_no === employee.emp_no }]"
                                @click="selectEmployee(employee)">
                                <td style="padding: 12px; border-bottom: 1px solid #e4e7ed;">{{ employee.emp_name }}</td>
                                <td style="padding: 12px; border-bottom: 1px solid #e4e7ed;">{{ employee.emp_no }}</td>
                                <td style="padding: 12px; border-bottom: 1px solid #e4e7ed;">{{ employee.first_dept_name }}</td>
                                <td style="padding: 12px; border-bottom: 1px solid #e4e7ed;">
                                    <span class="tag-primary">{{ employee.total_days.toFixed(1) }}天</span>
                                </td>
                                <td style="padding: 12px; border-bottom: 1px solid #e4e7ed;">
                                    <span class="badge">{{ employee.project_count }}</span>
                                </td>
                                <td style="padding: 12px; border-bottom: 1px solid #e4e7ed;">
                                    <div class="progress-bar">
                                        <div class="progress-fill" :style="{ width: employee.efficiency + '%' }"></div>
                                    </div>
                                    <small>{{ employee.efficiency.toFixed(2) }}%</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- 分页组件 -->
                    <div class="pagination-container">
                        <el-pagination
                            v-model:current-page="pagination.currentPage"
                            v-model:page-size="pagination.pageSize"
                            :page-sizes="[10, 20, 50]"
                            :total="filteredEmployees.length"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange">
                        </el-pagination>
                    </div>
                </div>
                
                <!-- 右侧分析区域 -->
                <div class="right-panel">
                    <div class="panel-title">
                        投入分析 - {{ selectedEmployee?.emp_name || '请选择员工' }}
                        <span class="data-indicator data-real" title="真实数据：来自API接口">
                            <i class="el-icon-success"></i>
                        </span>
                    </div>
                    
                    <!-- 项目投入饼图 -->
                    <div class="chart-container">
                        <h4 style="margin-bottom: 15px;">
                            项目投入分布
                            <span class="data-indicator data-real" title="真实数据：来自API接口">
                                <i class="el-icon-success"></i>
                            </span>
                        </h4>
                        <div id="projectPieChart" class="chart-box"></div>
                    </div>
                    
                    <!-- 月度投入趋势图 -->
                    <div class="chart-container">
                        <h4 style="margin-bottom: 15px;">
                            月度投入趋势
                            <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                                <i class="el-icon-warning"></i>
                            </span>
                        </h4>
                        <div id="trendChart" class="chart-box"></div>
                    </div>
                    
                    <!-- 项目投入详情表 -->
                    <div class="project-detail-table">
                        <h4 style="margin-bottom: 15px;">项目投入详情<span class="data-indicator data-real" title="真实数据：来自API接口">
                            <i class="el-icon-success"></i>
                        </span></h4>
                        <table style="width: 100%;">
                            <thead>
                                <tr style="background-color: #fafafa;">
                                    <th style="padding: 10px; text-align: left; border-bottom: 1px solid #e4e7ed;">项目名称</th>
                                    <th style="padding: 10px; text-align: left; border-bottom: 1px solid #e4e7ed;">投入天数</th>
                                    <th style="padding: 10px; text-align: left; border-bottom: 1px solid #e4e7ed;">投入比例</th>
                                    <th style="padding: 10px; text-align: left; border-bottom: 1px solid #e4e7ed;">项目状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="project in selectedEmployeeProjects" :key="project.project_name">
                                    <td style="padding: 10px; border-bottom: 1px solid #e4e7ed;">{{ project.project_name }}</td>
                                    <td style="padding: 10px; border-bottom: 1px solid #e4e7ed;">{{ project.days.toFixed(1) }}天</td>
                                    <td style="padding: 10px; border-bottom: 1px solid #e4e7ed;">{{ project.percentage }}%</td>
                                    <td style="padding: 10px; border-bottom: 1px solid #e4e7ed;">
                                        <span class="tag-success">{{ project.status }}</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts/dist/echarts.min.js"></script>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElLoading } = ElementPlus;

        // API配置 - 自动检测是否使用代理
        const API_CONFIG = {
            // 如果当前页面是localhost:3002，使用代理；否则直接访问
            baseUrl: window.location.hostname === 'localhost' && window.location.port === '3000'
                ? '/api'  // 使用代理
                : 'https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/sql/list', // 直接访问
            headers: {
                'X-Funipaas-Authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJTTTNXaXRoU00yIn0.eyJ1YXRfdWlkIjoiNGZlNGFlNDMwNGQ5OWQyZmE5YWRmZDAwNGY2OWVlNWE5NTkxMDZlYjM5YWM3YzFjZDlkNGY5ODkzZmI2MmEzOTVjYjAyYWFiMzQzODQwNGZiMjE1ZGMwMjMyYzY3MmYxIiwidWF0X3NpZCI6ImQ3ODYzNWJhNTkzNTI1M2U3ZWJiYTZkZGY2YjA4YTU3MTAxNWY3ZTk4ZDVjYTJiYzgzZWY3NzkwMDVhYWRjNjJlMzg4MDhkODU3MGM5MGFhZjRkMjg2NjdkNmNlODVkODhjZWQ5MmQyOTNkMmI1NTgwMWRkNDBmODYwM2FkM2VjZDBjM2RlODM5OTVmZTI1NmYxNDFkMWI5MjMxZGNjZTNkODMyNWQ1NTkyMzU3MWYyMGZiYjA3OWM2YTFmY2ZiNjNjYmEyOGJjMGViZGU2Y2M5NDJjYzFjZTBkM2Q5ODkzIiwiZXhwIjoxNzUwNzMxMzMxfQ.MEUCIQDfZGBqYmmdOhRlH28LP5oUgLc17gkVDnpUAyEO5DLCqQIgJzAk9vMzeQAvTwD9PXqZseHek6utVLWCHNgEJsHSSJg',
                'X-Funipaas-Request-Hash': 'bf3ed4e23df69f35d553cfb4564a06df12ffe0f8b9eb39f036c5c35382e69a74',
                'X-Funipaas-Request-Id': '82cedb35-713b-10eb-2299-3d66e93081c7',
                'X-Funipaas-Tenant': 'dev',
                'Content-Type': 'application/json'
            }
        };

        // 显示当前使用的API地址
        console.log('当前API配置:', API_CONFIG.baseUrl);

        createApp({
            data() {
                return {
                    loading: false,
                    filterForm: {
                        currentMonth: this.getCurrentMonth(),
                        department: '',
                        empType: '',
                        search: ''
                    },
                    selectedEmployee: null,
                    employees: [],
                    rawData: [],
                    departmentOptions: [],
                    empTypeOptions: [],
                    pieChart: null,
                    trendChart: null,
                    pagination: {
                        currentPage: 1,
                        pageSize: 10
                    }
                }
            },
            computed: {
                filteredEmployees() {
                    let result = this.employees;

                    if (this.filterForm.department) {
                        result = result.filter(emp => emp.first_dept_name === this.filterForm.department);
                    }

                    if (this.filterForm.empType) {
                        result = result.filter(emp => emp.emp_type === this.filterForm.empType);
                    }

                    if (this.filterForm.search) {
                        result = result.filter(emp =>
                            emp.emp_name.includes(this.filterForm.search) ||
                            emp.emp_no.includes(this.filterForm.search)
                        );
                    }

                    return result;
                },
                paginatedEmployees() {
                    const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;
                    const end = start + this.pagination.pageSize;
                    return this.filteredEmployees.slice(start, end);
                },
                selectedEmployeeProjects() {
                    return this.selectedEmployee ? this.selectedEmployee.projects : [];
                }
            },
            mounted() {
                // 初始化时只加载一次数据
                this.loadData();
            },
            methods: {
                async loadData() {
                    this.loading = true;
                    try {
                        const response = await this.fetchData();
                        console.log('API响应数据:', response);

                        // 根据实际API返回结构获取数据列表
                        if (response.data && response.data.list) {
                            this.rawData = response.data.list;
                        } else if (Array.isArray(response.data)) {
                            this.rawData = response.data;
                        } else {
                            this.rawData = [];
                            console.warn('API返回数据格式异常:', response);
                        }

                        console.log('处理后的原始数据:', this.rawData);
                        this.processData();
                        this.extractFilterOptions();

                        // 默认选择第一个员工
                        if (this.employees.length > 0) {
                            this.selectedEmployee = this.employees[0];
                            this.$nextTick(() => {
                                this.initCharts();
                            });
                        }
                    } catch (error) {
                        console.error('加载数据失败:', error);
                        ElMessage.error('加载数据失败，请检查网络连接');
                    } finally {
                        this.loading = false;
                    }
                },
                async fetchData() {
                    const requestBody = {
                         "pageSize": 1000,
                        "pageNo": 1,
                        "pageIndex": 1,
                        "page_id": "794321405799424",
                        "extendData": {},
                        "params": {},
                        "tabType": "sql",
                        "sql_id": "174b7551-2aed-49df-a2ca-030dbaa1fb02",
                        "component_id": "6604d148-7d86-30f5-6a32-45017c0ce596",
                        "filter": this.buildFilters()
                    };

                    const response = await fetch(API_CONFIG.baseUrl, {
                        method: 'POST',
                        headers: API_CONFIG.headers,
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return await response.json();
                },
                getCurrentMonth() {
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth()).padStart(2, '0');
                    return `${year}-${month}`;
                },
                buildFilters() {
                    const filters = [];

                    // 年月过滤
                    if (this.filterForm.currentMonth) {
                        const [year, month] = this.filterForm.currentMonth.split('-');

                        filters.push({
                            key: "year",
                            operator: "EQUAL",
                            value: year,
                            component: "el-date-picker"
                        });

                        filters.push({
                            key: "month",
                            operator: "EQUAL",
                            value: parseInt(month).toString(),
                            component: "el-date-picker"
                        });
                    }

                    // 部门过滤
                    if (this.filterForm.department) {
                        filters.push({
                            key: "first_dept_name",
                            operator: "EQUAL",
                            value: this.filterForm.department
                        });
                    }

                    // 员工类型过滤
                    if (this.filterForm.empType) {
                        filters.push({
                            key: "emp_type",
                            operator: "EQUAL",
                            value: this.filterForm.empType
                        });
                    }

                    return filters;
                },
                processData() {
                    // 按员工分组统计数据
                    const employeeMap = new Map();

                    this.rawData.forEach(record => {
                        const empKey = record.emp_no;
                        if (!employeeMap.has(empKey)) {
                            employeeMap.set(empKey, {
                                emp_name: record.emp_name,
                                emp_no: record.emp_no,
                                first_dept_name: record.first_dept_name,
                                emp_type: record.emp_type,
                                total_days: 0,
                                project_count: 0,
                                projects: new Map(),
                                efficiency: 0
                            });
                        }

                        const employee = employeeMap.get(empKey);
                        employee.total_days += parseFloat(record.per_days || 0);

                        // 统计项目
                        const projKey = record.dev_proj_id;
                        if (!employee.projects.has(projKey)) {
                            employee.projects.set(projKey, {
                                project_name: record.dev_proj_name,
                                days: 0,
                                percentage: 0,
                                status: '进行中' // 默认状态，可根据实际情况调整
                            });
                            employee.project_count++;
                        }

                        employee.projects.get(projKey).days += parseFloat(record.per_days || 0);
                    });

                    // 转换为数组并计算百分比
                    this.employees = Array.from(employeeMap.values()).map(emp => {
                        const projectsArray = Array.from(emp.projects.values());
                        projectsArray.forEach(project => {
                            project.percentage = emp.total_days > 0 ?
                                ((project.days / emp.total_days) * 100).toFixed(1) : 0;
                        });

                        return {
                            ...emp,
                            projects: projectsArray,
                            efficiency: Math.min(100, Math.max(60, 70 + Math.random() * 30)) // 模拟效率指标
                        };
                    });
                },
                extractFilterOptions() {
                    // 提取部门选项
                    const departments = [...new Set(this.rawData.map(item => item.first_dept_name).filter(Boolean))];
                    this.departmentOptions = departments.map(dept => ({ label: dept, value: dept }));

                    // 提取员工类型选项
                    const empTypes = [...new Set(this.rawData.map(item => item.emp_type).filter(Boolean))];
                    this.empTypeOptions = empTypes.map(type => ({ label: type, value: type }));
                },
                selectEmployee(employee) {
                    this.selectedEmployee = employee;
                    this.updateCharts();
                },
                applyFilter() {
                    this.loadData();
                    ElMessage.success('筛选条件已应用');
                },
                resetFilter() {
                    this.filterForm = {
                        currentMonth: this.getCurrentMonth(),
                        department: '',
                        empType: '',
                        search: ''
                    };
                    this.pagination.currentPage = 1; // 重置到第一页
                    this.loadData();
                },
                handleSizeChange(val) {
                    this.pagination.pageSize = val;
                    this.pagination.currentPage = 1; // 重置到第一页
                },
                handleCurrentChange(val) {
                    this.pagination.currentPage = val;
                },
                initCharts() {
                    this.initPieChart();
                    this.initTrendChart();
                },
                updateCharts() {
                    if (this.pieChart && this.trendChart) {
                        this.updatePieChart();
                        this.updateTrendChart();
                    }
                },
                initPieChart() {
                    const chartDom = document.getElementById('projectPieChart');
                    this.pieChart = echarts.init(chartDom);
                    this.updatePieChart();
                },
                updatePieChart() {
                    if (!this.selectedEmployee) return;

                    // 计算总投入天数
                    const totalDays = this.selectedEmployee.projects.reduce((sum, project) => sum + project.days, 0);

                    const data = this.selectedEmployee.projects.map(project => {
                        const percentage = totalDays > 0 ? ((project.days / totalDays) * 100).toFixed(1) : 0;
                        return {
                            value: project.days,
                            name: `${project.project_name} (${percentage}%)`
                        };
                    });

                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}天 ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left'
                        },
                        series: [
                            {
                                name: '项目投入',
                                type: 'pie',
                                radius: '50%',
                                data: data,
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };

                    this.pieChart.setOption(option);
                },
                initTrendChart() {
                    const chartDom = document.getElementById('trendChart');
                    this.trendChart = echarts.init(chartDom);
                    this.updateTrendChart();
                },
                updateTrendChart() {
                    if (!this.selectedEmployee) return;

                    // 根据选中员工的项目数据生成月度趋势
                    const monthlyData = this.generateMonthlyTrend();

                    const option = {
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            data: ['投入天数']
                        },
                        xAxis: {
                            type: 'category',
                            data: monthlyData.months
                        },
                        yAxis: {
                            type: 'value',
                            name: '天数'
                        },
                        series: [
                            {
                                name: '投入天数',
                                type: 'line',
                                smooth: true,
                                data: monthlyData.values,
                                itemStyle: {
                                    color: '#409EFF'
                                },
                                areaStyle: {
                                    opacity: 0.3
                                }
                            }
                        ]
                    };

                    this.trendChart.setOption(option);
                },
                generateMonthlyTrend() {
                    // 生成最近6个月的数据
                    const months = [];
                    const values = [];
                    const currentDate = new Date();

                    for (let i = 5; i >= 0; i--) {
                        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
                        const monthStr = `${date.getMonth() + 1}月`;
                        months.push(monthStr);

                        // 模拟月度投入数据，实际应该从rawData中统计
                        const baseValue = this.selectedEmployee ? this.selectedEmployee.total_days / 6 : 0;
                        const randomFactor = 0.7 + Math.random() * 0.6;
                        values.push(Math.round(baseValue * randomFactor));
                    }

                    return { months, values };
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
