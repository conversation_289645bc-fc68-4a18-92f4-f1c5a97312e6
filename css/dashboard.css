/* 研发中心仪表盘样式 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f7fa;
    color: #2c3e50;
    line-height: 1.6;
}

/* 容器布局 */
.dashboard-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    min-height: 100vh;
}

/* 头部样式 */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.dashboard-title {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.dashboard-subtitle {
    font-size: 16px;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 统计卡片样式 */
.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
    font-variant-numeric: tabular-nums;
}

.stat-label {
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 内容网格 */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* 内容卡片 */
.content-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
    transition: box-shadow 0.3s ease;
}

.content-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid #ebeef5;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-content {
    padding: 25px;
}

/* 图表容器 */
.chart-container {
    height: 300px;
    width: 100%;
    position: relative;
}

/* 加载和错误状态 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #909399;
    font-size: 14px;
}

.error {
    color: #f56c6c;
    text-align: center;
    padding: 20px;
    background: #fef0f0;
    border-radius: 8px;
    border: 1px solid #fbc4c4;
}

/* 刷新按钮 */
.refresh-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 全宽样式 */
.full-width {
    grid-column: 1 / -1;
}

/* 表格样式优化 */
.el-table {
    border-radius: 8px;
    overflow: hidden;
}

.el-table th {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
}

.el-table td {
    border-bottom: 1px solid #f0f0f0;
}

.el-table tr:hover td {
    background-color: #f8f9fa;
}

/* 标签样式 */
.el-tag {
    border-radius: 6px;
    font-weight: 500;
}

/* 进度条样式 */
.el-progress-bar__outer {
    border-radius: 10px;
}

.el-progress-bar__inner {
    border-radius: 10px;
}

/* 选项卡样式 */
.el-tabs--card > .el-tabs__header .el-tabs__nav {
    border-radius: 8px 8px 0 0;
}

.el-tabs--card > .el-tabs__header .el-tabs__item {
    border-radius: 8px 8px 0 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 15px;
    }
    
    .dashboard-header {
        padding: 20px;
    }
    
    .dashboard-title {
        font-size: 24px;
    }
    
    .dashboard-subtitle {
        font-size: 14px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .content-grid {
        gap: 20px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .card-content {
        padding: 20px;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .refresh-btn {
        bottom: 20px;
        right: 20px;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 10px;
    }
    
    .stat-value {
        font-size: 28px;
    }
    
    .card-header {
        padding: 15px 20px;
    }
    
    .card-content {
        padding: 15px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card,
.content-card {
    animation: fadeIn 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
