<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发中心管理仪表盘</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">

    <!-- 自定义CSS -->
    <link rel="stylesheet" href="css/dashboard.css">
</head>
<body>
    <div id="app">
        <div class="dashboard-container">
            <!-- 头部 -->
            <div class="dashboard-header">
                <h1 class="dashboard-title">研发中心管理仪表盘</h1>
                <p class="dashboard-subtitle">实时监控项目进度、人员配置和资源利用情况</p>
            </div>
            
            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        📊
                    </div>
                    <div class="stat-value">{{ projectStats.total }}</div>
                    <div class="stat-label">总项目数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                        🚀
                    </div>
                    <div class="stat-value">{{ projectStats.inProgress }}</div>
                    <div class="stat-label">进行中项目</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                        👥
                    </div>
                    <div class="stat-value">{{ staffStats.total }}</div>
                    <div class="stat-label">团队人数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                        ✅
                    </div>
                    <div class="stat-value">{{ taskStats.completedTasks }}</div>
                    <div class="stat-label">已完成任务</div>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="content-grid">
                <!-- 项目进度监控 -->
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">📈 项目进度监控</h3>
                    </div>
                    <div class="card-content">
                        <div v-if="loading.projects" class="loading">
                            <el-icon class="is-loading"><Loading /></el-icon>
                            <span style="margin-left: 10px;">加载中...</span>
                        </div>
                        <div v-else-if="error.projects" class="error">
                            {{ error.projects }}
                        </div>
                        <div v-else>
                            <div id="projectProgressChart" class="chart-container"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 人员工作饱和度 -->
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">⚖️ 人员工作饱和度</h3>
                    </div>
                    <div class="card-content">
                        <div v-if="loading.workload" class="loading">
                            <el-icon class="is-loading"><Loading /></el-icon>
                            <span style="margin-left: 10px;">加载中...</span>
                        </div>
                        <div v-else-if="error.workload" class="error">
                            {{ error.workload }}
                        </div>
                        <div v-else>
                            <div id="workloadChart" class="chart-container"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 任务完成情况 -->
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">📋 任务完成情况</h3>
                    </div>
                    <div class="card-content">
                        <div v-if="loading.tasks" class="loading">
                            <el-icon class="is-loading"><Loading /></el-icon>
                            <span style="margin-left: 10px;">加载中...</span>
                        </div>
                        <div v-else-if="error.tasks" class="error">
                            {{ error.tasks }}
                        </div>
                        <div v-else>
                            <div id="taskChart" class="chart-container"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 部门资源配置 -->
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">🏢 部门资源配置</h3>
                    </div>
                    <div class="card-content">
                        <div v-if="loading.departments" class="loading">
                            <el-icon class="is-loading"><Loading /></el-icon>
                            <span style="margin-left: 10px;">加载中...</span>
                        </div>
                        <div v-else-if="error.departments" class="error">
                            {{ error.departments }}
                        </div>
                        <div v-else>
                            <div id="departmentChart" class="chart-container"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 详细数据表格 -->
            <div class="content-card full-width">
                <div class="card-header">
                    <h3 class="card-title">📊 详细数据视图</h3>
                </div>
                <div class="card-content">
                    <el-tabs v-model="activeTab" type="card">
                        <el-tab-pane label="项目列表" name="projects">
                            <el-table :data="projectList" style="width: 100%" stripe>
                                <el-table-column prop="dev_proj_name" label="项目名称" width="200"></el-table-column>
                                <el-table-column prop="dev_proj_status" label="状态" width="100">
                                    <template #default="scope">
                                        <el-tag :type="getStatusType(scope.row.dev_proj_status)">
                                            {{ getStatusText(scope.row.dev_proj_status) }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="dev_proj_pm" label="项目经理" width="120"></el-table-column>
                                <el-table-column prop="funi_proj_begin_date" label="开始时间" width="120"></el-table-column>
                                <el-table-column prop="funi_proj_end_date" label="结束时间" width="120"></el-table-column>
                                <el-table-column prop="dev_proj_cost" label="预算成本" width="120">
                                    <template #default="scope">
                                        ¥{{ formatNumber(scope.row.dev_proj_cost) }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="funi_proj_income" label="预期收入" width="120">
                                    <template #default="scope">
                                        ¥{{ formatNumber(scope.row.funi_proj_income) }}
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-tab-pane>
                        
                        <el-tab-pane label="人员列表" name="staff">
                            <el-table :data="staffList" style="width: 100%" stripe>
                                <el-table-column prop="creator_name" label="姓名" width="120"></el-table-column>
                                <el-table-column prop="first_dept_name" label="部门" width="150"></el-table-column>
                                <el-table-column prop="emp_type" label="员工类型" width="100"></el-table-column>
                                <el-table-column prop="projectCount" label="参与项目数" width="120"></el-table-column>
                                <el-table-column prop="totalDaysInvested" label="总投入天数" width="120"></el-table-column>
                                <el-table-column prop="saturationRate" label="饱和度" width="100">
                                    <template #default="scope">
                                        <el-progress 
                                            :percentage="scope.row.saturationRate" 
                                            :color="getSaturationColor(scope.row.saturationRate)"
                                            :show-text="false"
                                        ></el-progress>
                                        <span style="margin-left: 10px;">{{ scope.row.saturationRate }}%</span>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-tab-pane>
                        
                        <el-tab-pane label="任务列表" name="tasks">
                            <el-table :data="taskList" style="width: 100%" stripe>
                                <el-table-column prop="job_content" label="任务内容" width="300"></el-table-column>
                                <el-table-column prop="creator_name" label="负责人" width="120"></el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template #default="scope">
                                        <el-tag :type="getTaskStatusType(scope.row.status)">
                                            {{ scope.row.status || '未开始' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="schedule" label="进度" width="120">
                                    <template #default="scope">
                                        <el-progress 
                                            :percentage="scope.row.schedule || 0" 
                                            :show-text="false"
                                        ></el-progress>
                                        <span style="margin-left: 10px;">{{ scope.row.schedule || 0 }}%</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="plan_begin_date" label="计划开始" width="120"></el-table-column>
                                <el-table-column prop="plan_end_date" label="计划结束" width="120"></el-table-column>
                                <el-table-column prop="estimate_man_hour" label="预估工时" width="100"></el-table-column>
                            </el-table>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
        </div>
        
        <!-- 刷新按钮 -->
        <el-button 
            class="refresh-btn" 
            type="primary" 
            :icon="Refresh" 
            circle 
            size="large"
            @click="refreshData"
            :loading="isRefreshing"
        ></el-button>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts@5/dist/echarts.min.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted, computed } = Vue;
        const { ElMessage, ElLoading } = ElementPlus;

        // API配置
        const API_CONFIG = {
            baseURL: 'https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/model/list',
            headers: {
                'Content-Type': 'application/json',
                'x-funipaas-authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJTTTNXaXRoU00yIn0.eyJ1YXRfdWlkIjoiNGZlNGFlNDMwNGQ5OWQyZmE5YWRmZDAwNGY2OWVlNWE5NTkxMDZlYjM5YWM3YzFjZDlkNGY5ODkzZmI2MmEzOTVjYjAyYWFiMzQzODQwNGZiMjE1ZGMwMjMyYzY3MmYxIiwidWF0X3NpZCI6ImJiODNiZWExM2Q3ZWM2YjBiZWNjMWEwNTkzMjZlMDg3YjNkMGMzN2I0NmU0ZWZjNmZlMGZlODc0MWE3Y2IxNDJiYWE4OWMyZjgyNmY3Y2QxZTBkOTg3NGM2MGEwMjhkNzc2YzcyMmExZWRhZTUzNWUzM2JiZDZmNWZmMDQ5OGUwOWViMzc2ODRjOWQ3MDFkODdkMDQwMGZlZjYyY2ZjNzk4ZGRjZjQ3NWYzNTA5MTA3ZWJlZDdkNjI2ZjBlOTEwZTNjYmEyOGJjMGViZGU2Y2M5NDJjYzFjZTBkM2Q5ODkzIiwiZXhwIjoxNzU0MTE5NTIwfQ.MEYCIQDz8CsA8sKxU6tZK8cQBrlSb5MP1eRz5ZFIrJx8FqLi_wIhAPaop-6KyxjNG1BusTzN03_VmfdDdLUNBKKp8eqlRu9B',
                'x-funipaas-request-hash': 'ce3b082aa9783a6bef7c71470699f003bf51c7622b9052554eb4073375f08a20',
                'x-funipaas-request-id': 'f0cc6871-56c0-219e-8f5c-11aa2957a301',
                'x-funipaas-tenant': 'dev'
            }
        };

        // API请求参数配置
        const API_PARAMS = {
            // 日报数据
            dailyReports: {
                "pageSize": 100,
                "pageNo": 1,
                "pageIndex": 1,
                "page_id": "785761401783296",
                "extendData": {},
                "tabType": "model",
                "model_id": "647a312d-ce90-4968-b159-5b531f85bc53",
                "sort": [{"key": "create_time", "mode": "desc"}],
                "component_id": "16cf3d7c-a537-1ec2-a69f-93e282ece000",
                "params": [{"logicalOperator": "", "conditions": [{"key": "is_deleted", "operator": "EQUAL", "value": "0", "logicalOperator": ""}]}],
                "joinFields": ["daily_info"],
                "filter": []
            },
            // 计划数据
            planData: {
                "pageSize": 100,
                "pageNo": 1,
                "pageIndex": 1,
                "page_id": "12734221815051265",
                "extendData": {},
                "tabType": "model",
                "model_id": "7fd9e661-8a61-451a-a9aa-7d8ff21aecb7",
                "sort": [],
                "component_id": "4171130f-b0af-5a6c-f09f-acf14502051b",
                "params": [{"logicalOperator": "", "conditions": [{"key": "is_deleted", "operator": "EQUAL", "value": "0", "logicalOperator": ""}]}],
                "joinFields": ["plan_info"],
                "filter": []
            },
            // 研发项目数据
            devProjects: {
                "pageSize": 100,
                "pageNo": 1,
                "pageIndex": 1,
                "page_id": "4313042434582529",
                "extendData": {},
                "tabType": "model",
                "model_id": "ad96ce63-35b0-4c0d-b2e6-42c9f8a793d8",
                "sort": [{"serialNumber": 1, "uuid": "ff0ca1a2-2f61-95a9-cbb0-582fe0156533", "key": "dev_proj_no", "mode": "desc"}],
                "component_id": "c2264b60-870f-220c-206b-e39ea1e97b4c",
                "params": [
                    {"logicalOperator": "", "conditions": [{"key": "dept_id", "operator": "LIKE", "value": ["17f0b34e-e41a-4782-8fbb-488874fbe7cc", "eeb003cd-52a2-4963-90cf-29e3dbe975da"], "logicalOperator": "AND"}, {"key": "is_deleted", "operator": "EQUAL", "value": "0", "logicalOperator": "AND"}]},
                    {"logicalOperator": "OR", "conditions": [{"key": "is_deleted", "operator": "EQUAL", "value": "0", "logicalOperator": ""}, {"key": "dev_proj_pm", "operator": "LIKE", "value": "ee7a8b8a-1100-44a9-819f-9dd25404c06b", "logicalOperator": "AND"}]},
                    {"logicalOperator": "OR", "conditions": [{"key": "is_deleted", "operator": "EQUAL", "value": "0", "logicalOperator": ""}, {"key": "bus_doing", "operator": "IS_NULL", "logicalOperator": "AND"}]}
                ],
                "joinFields": [],
                "filter": []
            }
        };

        // 工具函数
        const utils = {
            // 分组函数
            groupBy(array, key) {
                return array.reduce((groups, item) => {
                    const group = item[key];
                    if (!groups[group]) {
                        groups[group] = { key: group, items: [] };
                    }
                    groups[group].items.push(item);
                    return groups;
                }, {});
            },

            // 求和函数
            sum(array, key) {
                return array.reduce((total, item) => total + (item[key] || 0), 0);
            },

            // 平均值函数
            average(array, key) {
                if (array.length === 0) return 0;
                return this.sum(array, key) / array.length;
            },

            // 去重函数
            unique(array, key) {
                return array.filter((item, index, self) =>
                    index === self.findIndex(t => t[key] === item[key])
                );
            },

            // 格式化数字
            formatNumber(num) {
                if (!num) return '0';
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            },

            // API请求函数
            async fetchData(params) {
                try {
                    const response = await fetch(API_CONFIG.baseURL, {
                        method: 'POST',
                        headers: API_CONFIG.headers,
                        body: JSON.stringify(params)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    if (data.status === 200 && data.success) {
                        return data.data;
                    } else {
                        throw new Error(data.message || '请求失败');
                    }
                } catch (error) {
                    console.error('API请求错误:', error);
                    throw error;
                }
            }
        };

        // 创建Vue应用
        const app = createApp({
            setup() {
                // 响应式数据
                const activeTab = ref('projects');
                const isRefreshing = ref(false);

                // 原始数据
                const rawData = reactive({
                    dailyReports: [],
                    planData: [],
                    devProjects: []
                });

                // 加载状态
                const loading = reactive({
                    projects: true,
                    workload: true,
                    tasks: true,
                    departments: true
                });

                // 错误状态
                const error = reactive({
                    projects: null,
                    workload: null,
                    tasks: null,
                    departments: null
                });

                // 计算属性 - 统计数据
                const projectStats = computed(() => {
                    const projects = rawData.devProjects;
                    return {
                        total: projects.length,
                        inProgress: projects.filter(p => p.dev_proj_status === 'in_prog').length,
                        completed: projects.filter(p => p.dev_proj_status === 'completed').length,
                        paused: projects.filter(p => p.dev_proj_status === 'paused').length
                    };
                });

                const staffStats = computed(() => {
                    const staff = utils.unique(rawData.dailyReports, 'creator_name');
                    return {
                        total: staff.length,
                        byDept: utils.groupBy(rawData.dailyReports, 'first_dept_name'),
                        byType: utils.groupBy(rawData.dailyReports, 'emp_type')
                    };
                });

                const taskStats = computed(() => {
                    const tasks = rawData.planData.map(p => p.plan_info).filter(Boolean);
                    return {
                        totalTasks: tasks.length,
                        completedTasks: tasks.filter(t => t.status === 'completed').length,
                        inProgressTasks: tasks.filter(t => t.status === 'in_progress').length,
                        delayedTasks: tasks.filter(t => {
                            if (!t.plan_end_date || t.status === 'completed') return false;
                            return new Date(t.plan_end_date) < new Date();
                        }).length
                    };
                });

                // 计算属性 - 列表数据
                const projectList = computed(() => {
                    return rawData.devProjects.map(project => ({
                        ...project,
                        dev_proj_pm: project.dev_proj_pm || '未分配'
                    }));
                });

                const staffList = computed(() => {
                    const staffMap = new Map();

                    // 处理日报数据
                    rawData.dailyReports.forEach(report => {
                        const key = report.creator_name;
                        if (!staffMap.has(key)) {
                            staffMap.set(key, {
                                creator_name: key,
                                first_dept_name: report.first_dept_name,
                                emp_type: report.emp_type,
                                projectCount: new Set(),
                                totalDaysInvested: 0
                            });
                        }

                        const staff = staffMap.get(key);
                        if (report.daily_info && report.daily_info.dev_proj_name) {
                            staff.projectCount.add(report.daily_info.dev_proj_name);
                        }
                        if (report.daily_info && report.daily_info.days_invested) {
                            staff.totalDaysInvested += report.daily_info.days_invested;
                        }
                    });

                    return Array.from(staffMap.values()).map(staff => ({
                        ...staff,
                        projectCount: staff.projectCount.size,
                        saturationRate: Math.min(Math.round((staff.totalDaysInvested / 22) * 100), 150) // 假设月工作日22天
                    }));
                });

                const taskList = computed(() => {
                    return rawData.planData.map(plan => ({
                        ...plan,
                        ...plan.plan_info,
                        creator_name: plan.creator_name
                    })).filter(task => task.job_content);
                });

                // 数据加载函数
                const loadData = async () => {
                    try {
                        // 并行加载所有数据
                        const [dailyData, planDataResult, projectData] = await Promise.all([
                            utils.fetchData(API_PARAMS.dailyReports),
                            utils.fetchData(API_PARAMS.planData),
                            utils.fetchData(API_PARAMS.devProjects)
                        ]);

                        rawData.dailyReports = dailyData.list || [];
                        rawData.planData = planDataResult.list || [];
                        rawData.devProjects = projectData.list || [];

                        // 更新加载状态
                        Object.keys(loading).forEach(key => {
                            loading[key] = false;
                            error[key] = null;
                        });

                        // 初始化图表
                        setTimeout(() => {
                            initCharts();
                        }, 100);

                        ElMessage.success('数据加载成功');
                    } catch (err) {
                        console.error('数据加载失败:', err);
                        Object.keys(loading).forEach(key => {
                            loading[key] = false;
                            error[key] = '数据加载失败: ' + err.message;
                        });
                        ElMessage.error('数据加载失败');
                    }
                };

                // 刷新数据
                const refreshData = async () => {
                    isRefreshing.value = true;
                    Object.keys(loading).forEach(key => {
                        loading[key] = true;
                        error[key] = null;
                    });

                    await loadData();
                    isRefreshing.value = false;
                };

                // 初始化图表
                const initCharts = () => {
                    initProjectProgressChart();
                    initWorkloadChart();
                    initTaskChart();
                    initDepartmentChart();
                };

                // 项目进度图表
                const initProjectProgressChart = () => {
                    const chart = echarts.init(document.getElementById('projectProgressChart'));
                    const projects = rawData.devProjects.slice(0, 10); // 显示前10个项目

                    const option = {
                        title: {
                            text: '项目进度概览',
                            left: 'center',
                            textStyle: { fontSize: 14 }
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'shadow' }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'value',
                            max: 100
                        },
                        yAxis: {
                            type: 'category',
                            data: projects.map(p => p.dev_proj_name?.substring(0, 15) + '...' || '未命名项目')
                        },
                        series: [{
                            name: '进度',
                            type: 'bar',
                            data: projects.map(p => {
                                // 简单的进度计算逻辑
                                const startDate = new Date(p.funi_proj_begin_date);
                                const endDate = new Date(p.funi_proj_end_date);
                                const now = new Date();

                                if (startDate > now) return 0;
                                if (endDate < now) return 100;

                                const total = endDate - startDate;
                                const elapsed = now - startDate;
                                return Math.round((elapsed / total) * 100);
                            }),
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                    { offset: 0, color: '#667eea' },
                                    { offset: 1, color: '#764ba2' }
                                ])
                            }
                        }]
                    };

                    chart.setOption(option);
                    window.addEventListener('resize', () => chart.resize());
                };

                // 工作饱和度图表
                const initWorkloadChart = () => {
                    const chart = echarts.init(document.getElementById('workloadChart'));
                    const staff = staffList.value.slice(0, 10);

                    const option = {
                        title: {
                            text: '人员工作饱和度',
                            left: 'center',
                            textStyle: { fontSize: 14 }
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'shadow' }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: staff.map(s => s.creator_name),
                            axisLabel: {
                                rotate: 45
                            }
                        },
                        yAxis: {
                            type: 'value',
                            max: 150,
                            axisLabel: {
                                formatter: '{value}%'
                            }
                        },
                        series: [{
                            name: '饱和度',
                            type: 'bar',
                            data: staff.map(s => s.saturationRate),
                            itemStyle: {
                                color: (params) => {
                                    const value = params.value;
                                    if (value > 100) return '#f56c6c';
                                    if (value > 80) return '#e6a23c';
                                    return '#67c23a';
                                }
                            }
                        }]
                    };

                    chart.setOption(option);
                    window.addEventListener('resize', () => chart.resize());
                };

                // 任务完成情况图表
                const initTaskChart = () => {
                    const chart = echarts.init(document.getElementById('taskChart'));
                    const stats = taskStats.value;

                    const option = {
                        title: {
                            text: '任务完成情况',
                            left: 'center',
                            textStyle: { fontSize: 14 }
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c} ({d}%)'
                        },
                        legend: {
                            bottom: '5%',
                            left: 'center'
                        },
                        series: [{
                            name: '任务状态',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            center: ['50%', '45%'],
                            data: [
                                { value: stats.completedTasks, name: '已完成', itemStyle: { color: '#67c23a' } },
                                { value: stats.inProgressTasks, name: '进行中', itemStyle: { color: '#409eff' } },
                                { value: stats.delayedTasks, name: '延期', itemStyle: { color: '#f56c6c' } },
                                {
                                    value: stats.totalTasks - stats.completedTasks - stats.inProgressTasks - stats.delayedTasks,
                                    name: '未开始',
                                    itemStyle: { color: '#e6a23c' }
                                }
                            ],
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }]
                    };

                    chart.setOption(option);
                    window.addEventListener('resize', () => chart.resize());
                };

                // 部门资源配置图表
                const initDepartmentChart = () => {
                    const chart = echarts.init(document.getElementById('departmentChart'));
                    const deptGroups = utils.groupBy(rawData.dailyReports, 'first_dept_name');
                    const deptData = Object.values(deptGroups).map(group => ({
                        name: group.key,
                        value: group.items.length
                    }));

                    const option = {
                        title: {
                            text: '部门人员分布',
                            left: 'center',
                            textStyle: { fontSize: 14 }
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}人 ({d}%)'
                        },
                        legend: {
                            bottom: '5%',
                            left: 'center'
                        },
                        series: [{
                            name: '部门人员',
                            type: 'pie',
                            radius: '60%',
                            center: ['50%', '45%'],
                            data: deptData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }]
                    };

                    chart.setOption(option);
                    window.addEventListener('resize', () => chart.resize());
                };

                // 辅助方法
                const getStatusType = (status) => {
                    const statusMap = {
                        'in_prog': 'primary',
                        'completed': 'success',
                        'paused': 'warning',
                        'cancelled': 'danger'
                    };
                    return statusMap[status] || 'info';
                };

                const getStatusText = (status) => {
                    const statusMap = {
                        'in_prog': '进行中',
                        'completed': '已完成',
                        'paused': '暂停',
                        'cancelled': '已取消'
                    };
                    return statusMap[status] || '未知';
                };

                const getTaskStatusType = (status) => {
                    const statusMap = {
                        'completed': 'success',
                        'in_progress': 'primary',
                        'pending': 'warning'
                    };
                    return statusMap[status] || 'info';
                };

                const getSaturationColor = (rate) => {
                    if (rate > 100) return '#f56c6c';
                    if (rate > 80) return '#e6a23c';
                    return '#67c23a';
                };

                const formatNumber = utils.formatNumber;

                // 生命周期
                onMounted(() => {
                    loadData();
                });

                return {
                    // 响应式数据
                    activeTab,
                    isRefreshing,
                    loading,
                    error,

                    // 计算属性
                    projectStats,
                    staffStats,
                    taskStats,
                    projectList,
                    staffList,
                    taskList,

                    // 方法
                    refreshData,
                    getStatusType,
                    getStatusText,
                    getTaskStatusType,
                    getSaturationColor,
                    formatNumber,

                    // Icons
                    Loading: ElementPlusIconsVue.Loading,
                    Refresh: ElementPlusIconsVue.Refresh
                };
            }
        });

        // 使用Element Plus
        app.use(ElementPlus);

        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
