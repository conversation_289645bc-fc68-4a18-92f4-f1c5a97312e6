# 研发中心管理仪表盘 - 部署指南

## 📋 概述

本文档提供了研发中心管理仪表盘的完整部署指南，包括本地开发、测试和生产环境部署。

## 📁 项目文件结构

```
dashboard-dev/
├── index.html              # 主仪表盘页面
├── test.html               # API测试页面
├── css/
│   └── dashboard.css       # 自定义样式文件
├── 仪表盘需求.md           # 需求文档
├── 仪表盘功能文档.md       # 功能规格文档
├── README.md               # 项目说明文档
├── 部署指南.md             # 本文档
└── 快速启动指南.md         # 快速启动说明
```

## 🚀 快速部署

### 方式一：直接打开（最简单）

1. **下载项目文件**
   ```bash
   # 确保所有文件都在同一目录下
   ls -la
   # 应该看到 index.html, css/, test.html 等文件
   ```

2. **直接打开**
   ```bash
   # macOS
   open index.html
   
   # Windows
   start index.html
   
   # Linux
   xdg-open index.html
   ```

### 方式二：本地HTTP服务器

1. **使用Python（推荐）**
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   
   # 然后访问 http://localhost:8000
   ```

2. **使用Node.js**
   ```bash
   # 安装serve
   npm install -g serve
   
   # 启动服务器
   serve .
   
   # 或使用npx（无需安装）
   npx serve .
   ```

3. **使用PHP**
   ```bash
   php -S localhost:8000
   ```

### 方式三：Web服务器部署

1. **Apache部署**
   ```bash
   # 将项目文件复制到Apache文档根目录
   cp -r dashboard-dev/ /var/www/html/
   
   # 访问 http://your-domain/dashboard-dev/
   ```

2. **Nginx部署**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       root /var/www/dashboard-dev;
       index index.html;
       
       location / {
           try_files $uri $uri/ =404;
       }
   }
   ```

## 🔧 配置说明

### API配置

在 `index.html` 中找到 `API_CONFIG` 对象：

```javascript
const API_CONFIG = {
    baseURL: 'https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/model/list',
    headers: {
        'Content-Type': 'application/json',
        'x-funipaas-authorization': 'your-token-here',
        'x-funipaas-request-hash': 'your-hash-here',
        'x-funipaas-request-id': 'your-request-id-here',
        'x-funipaas-tenant': 'dev'
    }
};
```

### 环境配置

1. **开发环境**
   - 使用本地HTTP服务器
   - 启用浏览器开发者工具
   - 使用测试API端点

2. **测试环境**
   - 部署到测试服务器
   - 配置测试环境API
   - 进行功能验证

3. **生产环境**
   - 部署到生产服务器
   - 配置生产API端点
   - 启用HTTPS
   - 配置缓存策略

## 🧪 测试验证

### 1. API连接测试

使用 `test.html` 页面进行API测试：

```bash
# 打开测试页面
open test.html

# 或通过HTTP服务器访问
http://localhost:8000/test.html
```

测试步骤：
1. 点击"运行全部测试"按钮
2. 检查每个API的连接状态
3. 确认数据返回正常

### 2. 功能测试

在主仪表盘页面测试：
1. **数据加载**：检查统计卡片是否显示数据
2. **图表渲染**：确认所有图表正常显示
3. **交互功能**：测试刷新、切换选项卡等
4. **响应式**：在不同设备尺寸下测试

### 3. 性能测试

1. **加载时间**：页面首次加载时间 < 3秒
2. **API响应**：数据请求响应时间 < 2秒
3. **图表渲染**：图表绘制时间 < 1秒

## 🔒 安全配置

### 1. HTTPS配置

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    root /var/www/dashboard-dev;
    index index.html;
}
```

### 2. 访问控制

```nginx
# IP白名单
location / {
    allow ***********/24;
    allow 10.0.0.0/8;
    deny all;
    
    try_files $uri $uri/ =404;
}
```

### 3. API安全

- 定期更新API认证token
- 使用HTTPS传输
- 实施请求频率限制

## 📊 监控和维护

### 1. 日志监控

```bash
# Nginx访问日志
tail -f /var/log/nginx/access.log

# 错误日志
tail -f /var/log/nginx/error.log
```

### 2. 性能监控

- 使用浏览器开发者工具监控网络请求
- 设置API响应时间告警
- 监控页面加载性能

### 3. 定期维护

- **每周**：检查API连接状态
- **每月**：更新依赖库版本
- **每季度**：性能优化和安全审查

## 🐛 故障排除

### 常见问题

1. **页面无法加载**
   ```bash
   # 检查文件权限
   chmod 644 index.html
   chmod 755 css/
   
   # 检查文件路径
   ls -la index.html css/dashboard.css
   ```

2. **API请求失败**
   ```javascript
   // 检查网络连接
   fetch('https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/model/list')
     .then(response => console.log(response.status))
     .catch(error => console.error(error));
   ```

3. **图表不显示**
   ```javascript
   // 检查ECharts加载
   console.log(typeof echarts);
   
   // 检查容器元素
   console.log(document.getElementById('projectProgressChart'));
   ```

### 调试步骤

1. **打开浏览器开发者工具**
   - Chrome: F12 或 Ctrl+Shift+I
   - Firefox: F12 或 Ctrl+Shift+I
   - Safari: Cmd+Option+I

2. **检查Console面板**
   - 查看JavaScript错误
   - 检查API请求状态
   - 验证数据格式

3. **检查Network面板**
   - 确认CDN资源加载
   - 检查API请求和响应
   - 验证请求头信息

## 📈 性能优化

### 1. 缓存策略

```nginx
# 静态资源缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# HTML文件缓存
location ~* \.html$ {
    expires 1h;
    add_header Cache-Control "public";
}
```

### 2. 压缩配置

```nginx
# 启用gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/javascript
    application/xml+rss
    application/json;
```

### 3. CDN优化

考虑使用国内CDN加速：
```html
<!-- 使用国内CDN -->
<script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>
<script src="https://cdn.jsdelivr.net/npm/element-plus/dist/index.full.js"></script>
<script src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>
```

## 📞 技术支持

### 联系方式
- **技术团队**：研发中心技术组
- **紧急联系**：项目负责人
- **文档更新**：定期维护和更新

### 支持范围
- 部署问题解决
- 配置参数调整
- 性能优化建议
- 功能扩展指导

---

**注意**：本文档会根据项目发展持续更新，请关注最新版本。
