# 投入分析系统

基于ElementPlus和ECharts的项目投入分析系统，支持人员、项目、部门三个维度的数据分析。

## 📁 文件说明

### 主要页面
- **person-analysis.html** - 人员维度分析页面
- **project-analysis.html** - 项目维度分析页面
- **department-analysis.html** - 部门维度分析页面
- **api-test.html** - API连接测试页面
- **mock-data-fallback.html** - Mock数据演示页面（API无法访问时使用）

## 🚀 快速开始

### 1. 直接使用
所有HTML文件都是独立的，可以直接在浏览器中打开使用：
```bash
# 在浏览器中打开任意文件
open person-analysis.html
open project-analysis.html
open department-analysis.html
```

### 2. API测试
首先测试API连接是否正常：
```bash
open api-test.html
```
点击"测试API连接"按钮，检查是否能正常获取数据。

## 📊 功能特性

### 人员维度分析 (person-analysis.html)
- 📋 人员列表展示（姓名、工号、部门、总投入天数、项目数量、效率指标）
- 🥧 项目投入分布饼图
- 📈 月度投入趋势折线图
- 📋 项目投入详情表
- 🔍 筛选功能（时间范围、部门、员工类型、搜索）

### 项目维度分析 (project-analysis.html)
- 📊 项目概览卡片（总项目数、进行中、已完成、总投入人月）
- 📋 项目列表表格（项目信息、状态、投入进度、团队规模、重要程度）
- 🍩 团队投入分布环形图
- 📊 投入进度对比柱状图
- 🔍 筛选功能（时间范围、项目状态、项目类型、重要程度、搜索）

### 部门维度分析 (department-analysis.html)
- 🌳 部门组织架构树（副总级→一级→二级部门）
- 📊 部门概览卡片（项目数量、参与人员、总投入）
- 🥧 部门项目分布饼图
- 🕸️ 部门效率对比雷达图
- 🏆 项目投入排行榜
- 🔍 筛选功能（时间范围、部门层级、项目状态、搜索）

## 🔧 技术架构

### 前端技术栈
- **Vue 3** - 响应式框架
- **Element Plus** - UI组件库
- **ECharts** - 数据可视化图表库
- **原生CSS** - 样式设计

### API集成
- **接口地址**: `https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/sql/list`
- **请求方式**: POST
- **数据源**: project_employee_input表
- **认证方式**: X-Funipaas-Authorization header

### 数据处理
- 自动从API获取原始数据
- 按维度分组统计（人员/项目/部门）
- 动态生成筛选选项
- 实时计算投入比例和进度

## 📋 API参数说明

### 请求体结构
```json
{
  "pageSize": 1000,
  "pageNo": 1,
  "pageIndex": 1,
  "page_id": "13086478725215233",
  "extendData": {},
  "tabType": "model",
  "model_id": "7518e775-368f-47b8-a87c-32a146f616d2",
  "sort": [],
  "component_id": "7a1ee26e-7226-99bf-5f3e-cdf3bcf39f5e",
  "params": [
    {
      "logicalOperator": "",
      "conditions": [
        {
          "key": "is_deleted",
          "operator": "EQUAL", 
          "value": "0",
          "logicalOperator": ""
        }
      ]
    }
  ],
  "joinFields": [],
  "filter": [
    {
      "key": "year",
      "operator": "EQUAL",
      "value": "2025",
      "component": "el-date-picker"
    },
    {
      "key": "month", 
      "operator": "BETWEEN",
      "value": "2025-01,2025-05",
      "component": "el-date-picker"
    }
  ]
}
```

### 筛选条件
在`filter`数组中添加筛选条件：
- **时间筛选**: year + month字段
- **部门筛选**: first_dept_name字段
- **员工类型筛选**: emp_type字段

## 🎯 数据字段映射

### 核心字段
- `emp_name` - 员工姓名
- `emp_no` - 员工编号
- `emp_type` - 员工类型
- `first_dept_name` - 一级部门
- `deputy_dept_name` - 副总级部门
- `dev_proj_name` - 研发项目名称
- `dev_proj_no` - 研发项目编号
- `dev_proj_job_name` - 产品/工作项
- `per_days` - 投入天数
- `year` - 年份
- `month` - 月份

## 🔍 使用说明

### 1. 筛选数据
- 选择时间范围（月份区间）
- 选择部门、员工类型等条件
- 点击"应用筛选"按钮

### 2. 查看分析
- 点击列表项查看详细分析
- 图表会自动更新显示对应数据
- 支持交互式图表操作

### 3. 数据导出
- 图表支持右键保存为图片
- 表格数据可复制粘贴到Excel

## ⚠️ 注意事项

### API访问
- 需要有效的认证token
- 可能存在CORS跨域限制
- 建议在服务器环境中部署使用

### 数据计算
- 投入天数自动转换为人月（÷22个工作日）
- 效率指标为模拟数据
- 项目预估投入为计算值

### 浏览器兼容
- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 需要支持ES6+语法
- 建议使用最新版本浏览器

## 🐛 故障排除

### API连接失败 (404错误)
**问题**: API返回404 Not Found错误

**可能原因**:
1. API接口路径不正确
2. 服务器端接口已变更
3. 认证token过期
4. 请求参数格式错误

**解决方案**:
1. **使用API测试工具**: 打开`api-test.html`进行诊断
2. **测试多个接口**: 点击"测试所有接口"尝试不同的API路径
3. **确认接口地址**: 联系API提供方确认正确的接口路径
4. **更新认证信息**: 获取最新的token和请求头
5. **使用Mock数据**: 如果API暂时无法访问，使用`mock-data-fallback.html`

### 常见API路径变更
如果原接口`/model/lis`不可用，尝试以下路径：
- `/model/list` - 标准列表接口
- `/model/query` - 查询接口
- `/data/list` - 数据列表接口
- `/api/v1/data` - RESTful接口

### CORS跨域问题
**问题**: 浏览器控制台显示CORS错误

**解决方案**:
1. 部署到HTTP服务器（推荐）
2. 使用代理服务器
3. 配置服务器端CORS头

### 数据显示异常
1. 检查API返回数据格式
2. 确认字段名称匹配
3. 验证数据类型正确
4. 查看浏览器控制台错误信息

### 图表不显示
1. 检查浏览器控制台错误
2. 确认ECharts库加载成功
3. 验证数据格式正确
4. 检查容器元素是否存在

## 📞 技术支持

如有问题，请检查：
1. 浏览器控制台错误信息
2. 网络请求状态
3. API返回数据格式
4. 认证token有效性
