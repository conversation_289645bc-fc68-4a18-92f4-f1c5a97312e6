# 研发中心管理仪表盘

一个基于Vue3 + Element Plus + ECharts的现代化研发管理仪表盘，用于实时监控项目进度、人员配置和资源利用情况。

## 🚀 功能特性

### 📊 核心功能模块

1. **总览仪表盘**
   - 项目总数、进行中项目、团队人数、已完成任务等关键指标
   - 实时数据更新和可视化展示

2. **项目进度监控**
   - 项目进度条形图
   - 项目状态分布
   - 项目时间线追踪

3. **人员工作饱和度分析**
   - 个人工作饱和度柱状图
   - 饱和度预警（超过100%显示红色）
   - 部门人员分布饼图

4. **任务完成情况**
   - 任务状态饼图（已完成、进行中、延期、未开始）
   - 任务详细列表视图

5. **详细数据视图**
   - 项目列表：包含项目名称、状态、项目经理、时间、预算等
   - 人员列表：包含姓名、部门、参与项目数、饱和度等
   - 任务列表：包含任务内容、负责人、状态、进度等

### 🎨 界面特性

- **响应式设计**：支持桌面端、平板和移动端
- **现代化UI**：基于Element Plus组件库，界面美观易用
- **实时数据**：支持数据自动刷新和手动刷新
- **交互式图表**：基于ECharts的丰富图表展示
- **状态指示**：不同颜色标识不同状态和优先级

## 🛠️ 技术栈

- **前端框架**：Vue 3 (CDN)
- **UI组件库**：Element Plus (CDN)
- **图表库**：ECharts 5 (CDN)
- **样式**：CSS3 + Flexbox + Grid
- **数据源**：RESTful API

## 📁 项目结构

```
dashboard-dev/
├── index.html              # 主页面文件
├── css/
│   └── dashboard.css       # 自定义样式文件
├── 仪表盘需求.md           # 需求文档
├── 仪表盘功能文档.md       # 功能规格文档
└── README.md               # 项目说明文档
```

## 🚀 快速开始

### 1. 环境要求

- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 网络连接（用于加载CDN资源和API数据）

### 2. 部署方式

#### 方式一：直接打开
```bash
# 直接用浏览器打开index.html文件
open index.html
```

#### 方式二：本地服务器
```bash
# 使用Python启动本地服务器
python -m http.server 8000

# 或使用Node.js
npx serve .

# 然后访问 http://localhost:8000
```

#### 方式三：Web服务器部署
将整个项目文件夹上传到Web服务器即可。

### 3. 配置说明

#### API配置
在`index.html`中的`API_CONFIG`对象包含了API的配置信息：

```javascript
const API_CONFIG = {
    baseURL: 'https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/model/list',
    headers: {
        'Content-Type': 'application/json',
        'x-funipaas-authorization': 'your-token-here',
        // ... 其他headers
    }
};
```

#### 数据源配置
`API_PARAMS`对象定义了不同数据源的请求参数：
- `dailyReports`: 日报数据
- `planData`: 计划数据
- `devProjects`: 研发项目数据

## 📊 数据说明

### 数据来源
仪表盘从以下API端点获取数据：
- 日报数据：用于人员工作量统计
- 计划数据：用于任务管理和进度跟踪
- 项目数据：用于项目状态和进度监控

### 数据处理
- **前端聚合**：所有数据计算在前端完成
- **实时更新**：支持定时刷新和手动刷新
- **缓存机制**：避免频繁API调用

### 关键指标计算

1. **项目统计**
   - 总项目数：所有项目记录数
   - 进行中项目：状态为'in_prog'的项目数
   - 完成项目：状态为'completed'的项目数

2. **人员饱和度**
   - 计算公式：(总投入天数 / 22工作日) × 100%
   - 预警阈值：>100%为超负荷，>80%为高负荷

3. **任务完成率**
   - 基于任务状态统计
   - 延期任务：超过计划结束时间且未完成

## 🎯 使用指南

### 1. 主界面导航
- **统计卡片**：显示关键KPI指标
- **图表区域**：可视化数据展示
- **数据表格**：详细数据查看

### 2. 交互操作
- **刷新数据**：点击右下角刷新按钮
- **切换视图**：使用选项卡切换不同数据视图
- **图表交互**：鼠标悬停查看详细信息

### 3. 响应式适配
- **桌面端**：完整功能展示
- **平板端**：自适应布局调整
- **移动端**：优化触摸操作

## 🔧 自定义配置

### 1. 样式定制
修改`css/dashboard.css`文件来自定义样式：
- 颜色主题
- 字体大小
- 布局间距
- 动画效果

### 2. 功能扩展
在`index.html`中可以：
- 添加新的图表类型
- 扩展数据处理逻辑
- 增加新的统计指标
- 自定义交互功能

### 3. API适配
根据实际API接口调整：
- 请求参数格式
- 响应数据结构
- 错误处理逻辑
- 认证方式

## 🐛 故障排除

### 常见问题

1. **数据加载失败**
   - 检查网络连接
   - 验证API认证信息
   - 查看浏览器控制台错误信息

2. **图表显示异常**
   - 确保ECharts CDN加载成功
   - 检查容器元素是否存在
   - 验证数据格式是否正确

3. **样式显示问题**
   - 检查CSS文件路径
   - 验证Element Plus CDN加载
   - 清除浏览器缓存

### 调试方法
1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 检查Network面板的API请求状态
4. 使用Vue DevTools调试组件状态

## 📝 更新日志

### v1.0.0 (2025-08-01)
- ✨ 初始版本发布
- 📊 实现核心仪表盘功能
- 🎨 响应式界面设计
- 📱 移动端适配
- 🔄 实时数据刷新

## 📄 许可证

本项目仅供内部使用，请勿外传。

## 🤝 贡献指南

如需修改或扩展功能，请：
1. 详细测试修改内容
2. 更新相关文档
3. 确保兼容性
4. 提交变更说明

## 📞 技术支持

如遇技术问题，请联系研发中心技术团队。

## 📁 文件说明

### 主要页面
- **person-analysis.html** - 人员维度分析页面
- **project-analysis.html** - 项目维度分析页面
- **department-analysis.html** - 部门维度分析页面
- **api-test.html** - API连接测试页面
- **mock-data-fallback.html** - Mock数据演示页面（API无法访问时使用）

## 🚀 快速开始

### 1. 直接使用
所有HTML文件都是独立的，可以直接在浏览器中打开使用：
```bash
# 在浏览器中打开任意文件
open person-analysis.html
open project-analysis.html
open department-analysis.html
```

### 2. API测试
首先测试API连接是否正常：
```bash
open api-test.html
```
点击"测试API连接"按钮，检查是否能正常获取数据。

## 📊 功能特性

### 人员维度分析 (person-analysis.html)
- 📋 人员列表展示（姓名、工号、部门、总投入天数、项目数量、效率指标）
- 🥧 项目投入分布饼图
- 📈 月度投入趋势折线图
- 📋 项目投入详情表
- 🔍 筛选功能（时间范围、部门、员工类型、搜索）

### 项目维度分析 (project-analysis.html)
- 📊 项目概览卡片（总项目数、进行中、已完成、总投入人月）
- 📋 项目列表表格（项目信息、状态、投入进度、团队规模、重要程度）
- 🍩 团队投入分布环形图
- 📊 投入进度对比柱状图
- 🔍 筛选功能（时间范围、项目状态、项目类型、重要程度、搜索）

### 部门维度分析 (department-analysis.html)
- 🌳 部门组织架构树（副总级→一级→二级部门）
- 📊 部门概览卡片（项目数量、参与人员、总投入）
- 🥧 部门项目分布饼图
- 🕸️ 部门效率对比雷达图
- 🏆 项目投入排行榜
- 🔍 筛选功能（时间范围、部门层级、项目状态、搜索）

## 🔧 技术架构

### 前端技术栈
- **Vue 3** - 响应式框架
- **Element Plus** - UI组件库
- **ECharts** - 数据可视化图表库
- **原生CSS** - 样式设计

### API集成
- **接口地址**: `https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/sql/list`
- **请求方式**: POST
- **数据源**: project_employee_input表
- **认证方式**: X-Funipaas-Authorization header

### 数据处理
- 自动从API获取原始数据
- 按维度分组统计（人员/项目/部门）
- 动态生成筛选选项
- 实时计算投入比例和进度

## 📋 API参数说明

### 请求体结构
```json
{
  "pageSize": 1000,
  "pageNo": 1,
  "pageIndex": 1,
  "page_id": "13086478725215233",
  "extendData": {},
  "tabType": "model",
  "model_id": "7518e775-368f-47b8-a87c-32a146f616d2",
  "sort": [],
  "component_id": "7a1ee26e-7226-99bf-5f3e-cdf3bcf39f5e",
  "params": [
    {
      "logicalOperator": "",
      "conditions": [
        {
          "key": "is_deleted",
          "operator": "EQUAL", 
          "value": "0",
          "logicalOperator": ""
        }
      ]
    }
  ],
  "joinFields": [],
  "filter": [
    {
      "key": "year",
      "operator": "EQUAL",
      "value": "2025",
      "component": "el-date-picker"
    },
    {
      "key": "month", 
      "operator": "BETWEEN",
      "value": "2025-01,2025-05",
      "component": "el-date-picker"
    }
  ]
}
```

### 筛选条件
在`filter`数组中添加筛选条件：
- **时间筛选**: year + month字段
- **部门筛选**: first_dept_name字段
- **员工类型筛选**: emp_type字段

## 🎯 数据字段映射

### 核心字段
- `emp_name` - 员工姓名
- `emp_no` - 员工编号
- `emp_type` - 员工类型
- `first_dept_name` - 一级部门
- `deputy_dept_name` - 副总级部门
- `dev_proj_name` - 研发项目名称
- `dev_proj_no` - 研发项目编号
- `dev_proj_job_name` - 产品/工作项
- `per_days` - 投入天数
- `year` - 年份
- `month` - 月份

## 🔍 使用说明

### 1. 筛选数据
- 选择时间范围（月份区间）
- 选择部门、员工类型等条件
- 点击"应用筛选"按钮

### 2. 查看分析
- 点击列表项查看详细分析
- 图表会自动更新显示对应数据
- 支持交互式图表操作

### 3. 数据导出
- 图表支持右键保存为图片
- 表格数据可复制粘贴到Excel

## ⚠️ 注意事项

### API访问
- 需要有效的认证token
- 可能存在CORS跨域限制
- 建议在服务器环境中部署使用

### 数据计算
- 投入天数自动转换为人月（÷22个工作日）
- 效率指标为模拟数据
- 项目预估投入为计算值

### 浏览器兼容
- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 需要支持ES6+语法
- 建议使用最新版本浏览器

## 🐛 故障排除

### API连接失败 (404错误)
**问题**: API返回404 Not Found错误

**可能原因**:
1. API接口路径不正确
2. 服务器端接口已变更
3. 认证token过期
4. 请求参数格式错误

**解决方案**:
1. **使用API测试工具**: 打开`api-test.html`进行诊断
2. **测试多个接口**: 点击"测试所有接口"尝试不同的API路径
3. **确认接口地址**: 联系API提供方确认正确的接口路径
4. **更新认证信息**: 获取最新的token和请求头
5. **使用Mock数据**: 如果API暂时无法访问，使用`mock-data-fallback.html`

### 常见API路径变更
如果原接口`/model/lis`不可用，尝试以下路径：
- `/model/list` - 标准列表接口
- `/model/query` - 查询接口
- `/data/list` - 数据列表接口
- `/api/v1/data` - RESTful接口

### CORS跨域问题
**问题**: 浏览器控制台显示CORS错误

**解决方案**:
1. 部署到HTTP服务器（推荐）
2. 使用代理服务器
3. 配置服务器端CORS头

### 数据显示异常
1. 检查API返回数据格式
2. 确认字段名称匹配
3. 验证数据类型正确
4. 查看浏览器控制台错误信息

### 图表不显示
1. 检查浏览器控制台错误
2. 确认ECharts库加载成功
3. 验证数据格式正确
4. 检查容器元素是否存在

## 📞 技术支持

如有问题，请检查：
1. 浏览器控制台错误信息
2. 网络请求状态
3. API返回数据格式
4. 认证token有效性
