# 角色定义
你现在是一名资深的系统架构专家，现在有一些表结构，你需要根据已有表结构设计出一套提供给部门负责人管理部门资源使用的一个仪表盘。
系统使用角色：研发部门负责人

# 表结构

### 1.日报表
```javascript
// 日报表数据结构
const DailyReportSchema = {
  tableName: 'model_4l0z8crqfbi',
  comment: '日报',
  fields: {
    post_name: { type: 'string', maxLength: 100, required: false, comment: '岗位名称' },
    group_leader_id: { type: 'string', maxLength: 100, required: false, comment: '小组长ID' },
    group_id: { type: 'string', maxLength: 100, required: false, comment: '所属小组' },
    first_dept_id: { type: 'string', maxLength: 100, required: false, comment: '员工一级部门ID' },
    first_dept_name: { type: 'string', maxLength: 100, required: false, comment: '员工一级部门名称' },
    deputy_dept_id: { type: 'string', maxLength: 100, required: false, comment: '副总级部门ID' },
    deputy_dept_name: { type: 'string', maxLength: 100, required: false, comment: '副总级部门名称' },
    daily_info: { type: 'string', maxLength: 100, required: false, comment: '日报详情' },
    emp_no: { type: 'string', maxLength: 100, required: true, comment: '员工编号' },
    emp_type: { type: 'string', maxLength: 80, required: false, comment: '员工类型' },
    emp_dept: { type: 'text', required: false, comment: '所属部门' },
    daily_date: { type: 'date', required: false, comment: '日报日期' },
    daily_titile: { type: 'string', maxLength: 100, required: true, comment: '日报标题' },
    deleter_name: { type: 'string', maxLength: 100, required: false, comment: '删除者' },
    updater_name: { type: 'string', maxLength: 100, required: false, comment: '修改者' },
    creator_name: { type: 'string', maxLength: 100, required: false, comment: '创建者' },
    business_status: { type: 'number', required: false, comment: '业务件状态' },
    creator_unit: { type: 'text', required: false, comment: '创建者所属部门' },
    bus_type: { type: 'string', maxLength: 100, required: false, comment: '业务类型' },
    bus_type_name: { type: 'string', maxLength: 100, required: false, comment: '业务类型描述' },
    bus_doing: { type: 'string', maxLength: 100, required: false, comment: '在办业务' },
    bus_doing_name: { type: 'string', maxLength: 100, required: false, comment: '在办业务描述' },
    failure: { type: 'number', required: false, comment: '失效状态' },
    failure_name: { type: 'string', maxLength: 100, required: false, comment: '失效状态描述' },
    lastId: { type: 'string', maxLength: 100, required: false, comment: '上手ID' },
    business_status_name: { type: 'string', maxLength: 100, required: false, comment: '业务件状态描述' },
    is_deleted: { type: 'boolean', required: false, comment: '是否删除' },
    deleter_id: { type: 'string', maxLength: 100, required: false, comment: '删除者ID' },
    updater_id: { type: 'string', maxLength: 100, required: false, comment: '修改者ID' },
    creator_id: { type: 'string', maxLength: 100, required: false, comment: '创建者ID' },
    delete_time: { type: 'datetime', required: false, comment: '删除时间' },
    update_time: { type: 'datetime', required: false, comment: '修改时间' },
    failure_time: { type: 'datetime', required: false, comment: '失效时间' },
    creator_unit_id: { type: 'string', maxLength: 100, required: false, comment: '创建者所属部门ID' },
    create_time: { type: 'datetime', required: false, comment: '创建时间' },
    remark: { type: 'string', maxLength: 100, required: false, comment: '备注' },
    business_id: { type: 'string', maxLength: 100, required: false, comment: '业务件id' },
    cid: { type: 'string', maxLength: 100, required: false, comment: '关联id' },
    id: { type: 'string', maxLength: 100, required: true, comment: 'ID', primaryKey: true }
  }
};
```

### 2.日报详情表
```javascript
// 日报详情表数据结构
const DailyReportDetailSchema = {
  tableName: 'model_4l0zbuidwvj',
  comment: '日报详情',
  fields: {
    deputy_dept_name: { type: 'string', maxLength: 100, required: false, comment: '副总级部门名称' },
    post_name: { type: 'string', maxLength: 100, required: false, comment: '岗位名称' },
    dev_proj_dep_name: { type: 'string', maxLength: 100, required: false, comment: '中心负责部门名称' },
    dev_proj_type: { type: 'string', maxLength: 100, required: false, comment: '研发项目类型' },
    dev_proj_job_name: { type: 'string', maxLength: 100, required: false, comment: '产品/工作项' },
    daily_id: { type: 'string', maxLength: 100, required: false, comment: '日报' },
    dev_proj_id: { type: 'string', maxLength: 100, required: true, comment: '研发项目' },
    region_name: { type: 'string', maxLength: 100, required: true, comment: '区域名称' },
    region_id: { type: 'string', maxLength: 100, required: false, comment: '区域' },
    dev_proj_job_id: { type: 'string', maxLength: 100, required: false, comment: '研发项目-系统-工作' },
    job_content: { type: 'string', maxLength: 2000, required: false, comment: '工作内容以及完成情况' },
    days_invested: { type: 'number', precision: 20, scale: 1, required: true, comment: '投入天数' },
    dev_proj_name: { type: 'string', maxLength: 100, required: false, comment: '研发项目名称' },
    dev_proj_no: { type: 'string', maxLength: 100, required: false, comment: '研发项目编号' },
    deleter_name: { type: 'string', maxLength: 100, required: false, comment: '删除者' },
    updater_name: { type: 'string', maxLength: 100, required: false, comment: '修改者' },
    creator_name: { type: 'string', maxLength: 100, required: false, comment: '创建者' },
    business_status: { type: 'number', required: false, comment: '业务件状态' },
    creator_unit: { type: 'text', required: false, comment: '创建者所属部门' },
    bus_type: { type: 'string', maxLength: 100, required: false, comment: '业务类型' },
    bus_type_name: { type: 'string', maxLength: 100, required: false, comment: '业务类型描述' },
    bus_doing: { type: 'string', maxLength: 100, required: false, comment: '在办业务' },
    bus_doing_name: { type: 'string', maxLength: 100, required: false, comment: '在办业务描述' },
    failure: { type: 'number', required: false, comment: '失效状态' },
    failure_name: { type: 'string', maxLength: 100, required: false, comment: '失效状态描述' },
    lastId: { type: 'string', maxLength: 100, required: false, comment: '上手ID' },
    business_status_name: { type: 'string', maxLength: 100, required: false, comment: '业务件状态描述' },
    is_deleted: { type: 'boolean', required: false, comment: '是否删除' },
    deleter_id: { type: 'string', maxLength: 100, required: false, comment: '删除者ID' },
    updater_id: { type: 'string', maxLength: 100, required: false, comment: '修改者ID' },
    creator_id: { type: 'string', maxLength: 100, required: false, comment: '创建者ID' },
    delete_time: { type: 'datetime', required: false, comment: '删除时间' },
    update_time: { type: 'datetime', required: false, comment: '修改时间' },
    failure_time: { type: 'datetime', required: false, comment: '失效时间' },
    creator_unit_id: { type: 'string', maxLength: 100, required: false, comment: '创建者所属部门ID' },
    create_time: { type: 'datetime', required: false, comment: '创建时间' },
    remark: { type: 'string', maxLength: 100, required: false, comment: '备注' },
    business_id: { type: 'string', maxLength: 100, required: false, comment: '业务件id' },
    cid: { type: 'string', maxLength: 100, required: false, comment: '关联id' },
    id: { type: 'string', maxLength: 100, required: true, comment: 'ID', primaryKey: true }
  }
};
```

### 3.研发项目表
```javascript
// 研发项目表数据结构
const DevProjectSchema = {
  tableName: 'model_4l0zlfamkrh',
  comment: '研发项目',
  fields: {
    id: { type: 'string', maxLength: 100, required: true, comment: 'ID', primaryKey: true },
    dev_proj_no: { type: 'string', maxLength: 100, required: false, comment: '研发项目编号' },
    dev_proj_name: { type: 'string', maxLength: 100, required: false, comment: '研发项目名称' },
    dev_proj_sys_info: { type: 'string', maxLength: 100, required: false, comment: '产品清单' },
    funi_proj_id: { type: 'string', maxLength: 80, required: false, comment: '公司项目ID' },
    funi_proj_sn: { type: 'string', maxLength: 100, required: false, comment: '公司项目编号' },
    funi_proj_name: { type: 'string', maxLength: 100, required: false, comment: '公司项目名称' },
    dev_proj_status: { type: 'string', maxLength: 30, required: false, comment: '研发项目状态' },
    dev_proj_ailas: { type: 'string', maxLength: 100, required: false, comment: '研发项目别名' },
    region_code: { type: 'string', maxLength: 100, required: false, comment: '区域代码' },
    deleter_name: { type: 'string', maxLength: 100, required: false, comment: '删除者' },
    updater_name: { type: 'string', maxLength: 100, required: false, comment: '修改者' },
    creator_name: { type: 'string', maxLength: 100, required: false, comment: '创建者' },
    business_status: { type: 'number', required: false, comment: '业务件状态' },
    is_deleted: { type: 'boolean', required: false, comment: '是否删除' },
    deleter_id: { type: 'string', maxLength: 100, required: false, comment: '删除者ID' },
    updater_id: { type: 'string', maxLength: 100, required: false, comment: '修改者ID' },
    creator_id: { type: 'string', maxLength: 100, required: false, comment: '创建者ID' },
    delete_time: { type: 'datetime', required: false, comment: '删除时间' },
    update_time: { type: 'datetime', required: false, comment: '修改时间' },
    create_time: { type: 'datetime', required: false, comment: '创建时间' },
    remark: { type: 'text', required: false, comment: '备注' },
    business_id: { type: 'string', maxLength: 100, required: false, comment: '业务件id' },
    cid: { type: 'string', maxLength: 100, required: false, comment: '关联id' },
    creator_unit: { type: 'text', required: false, comment: '所属部门' },
    funi_proj_begin_date: { type: 'date', required: false, comment: '项目开始时间' },
    funi_proj_end_date: { type: 'date', required: false, comment: '项目结束时间' },
    dev_proj_cost: { type: 'decimal', precision: 10, scale: 2, required: false, comment: '预估研发成本' },
    funi_proj_income: { type: 'decimal', precision: 10, scale: 2, required: false, comment: '预估收入' },
    funi_proj_scope: { type: 'string', maxLength: 1000, required: false, comment: '项目范围' },
    funi_proj_target: { type: 'string', maxLength: 1000, required: false, comment: '项目目标' },
    dev_proj_pm: { type: 'text', required: false, comment: '项目经理' },
    dev_proj_pm_phone: { type: 'string', maxLength: 100, required: false, comment: '联系电话' },
    funi_proj_stakeholders: { type: 'string', maxLength: 2000, required: false, comment: '项目干系人' },
    dev_proj_dep: { type: 'text', required: false, comment: '中心负责部门' },
    dept_id: { type: 'string', maxLength: 100, required: false, comment: '对应业务部门ID' },
    dept_name: { type: 'string', maxLength: 100, required: false, comment: '对应业务部门名称' },
    dev_proj_income_exp: { type: 'string', maxLength: 2000, required: false, comment: '项目收益说明' },
    dev_proj_stage: { type: 'string', maxLength: 100, required: false, comment: '研发项目阶段' },
    dev_proj_pdsp_time: { type: 'date', required: false, comment: '研发项目结项时间' },
    dev_proj_terminate_time: { type: 'date', required: false, comment: '研发项目终止时间' },
    dev_proj_type: { type: 'string', maxLength: 100, required: false, comment: '研发项目类型' },
    region_id: { type: 'string', maxLength: 100, required: false, comment: '区域ID' },
    region_name: { type: 'string', maxLength: 100, required: false, comment: '区域名称' },
    bus_type: { type: 'string', maxLength: 200, required: false, comment: '业务类型' },
    bus_doing: { type: 'string', maxLength: 200, required: false, comment: '在办业务' },
    failure: { type: 'number', required: false, comment: '失效状态' },
    lastId: { type: 'string', maxLength: 200, required: false, comment: '上手ID' },
    bus_type_name: { type: 'string', maxLength: 200, required: false, comment: '业务类型描述' },
    bus_doing_name: { type: 'string', maxLength: 200, required: false, comment: '在办业务描述' },
    failure_name: { type: 'string', maxLength: 200, required: false, comment: '失效状态描述' },
    business_status_name: { type: 'string', maxLength: 200, required: false, comment: '业务件状态描述' },
    dev_proj_complexity: { type: 'string', maxLength: 100, required: false, comment: '研发项目复杂度' },
    dev_proj_degree: { type: 'string', maxLength: 100, required: false, comment: '研发项目重要程度' },
    dev_proj_merit_coefficient: { type: 'decimal', precision: 10, scale: 2, required: false, comment: '项目绩效系数' },
    failure_time: { type: 'datetime', required: false, comment: '失效时间' },
    dev_proj_change_cause: { type: 'string', maxLength: 1000, required: false, comment: '变更原因' },
    creator_unit_id: { type: 'string', maxLength: 200, required: false, comment: '创建者所属部门ID' },
    dev_proj_bus_dep: { type: 'text', required: false, comment: '对应业务部门' },
    pro_info: { type: 'string', maxLength: 100, required: false, comment: '关联产品明细' },
    dev_proj_bus_dep_pm: { type: 'text', required: false, comment: '对应业务部门经办人' },
    dev_proj_into_off_date: { type: 'string', maxLength: 100, required: false, comment: '研发投入截止时间' },
    dev_proj_add_cost: { type: 'decimal', precision: 20, scale: 2, required: false, comment: '变更增加研发成本' },
    dev_proj_pause_cause: { type: 'string', maxLength: 100, required: false, comment: '暂停投入原因' },
    dev_proj_imm: { type: 'number', required: false, comment: '研发投入人月' },
    dev_proj_alias: { type: 'string', maxLength: 120, required: false, comment: '项目别名' },
    dev_proj_end_cause: { type: 'string', maxLength: 1000, required: false, comment: '结项原因' },
    dev_proj_add_imm: { type: 'decimal', precision: 20, scale: 2, required: false, comment: '变更增加研发投入人月' },
    region_info: { type: 'text', required: false, comment: '项目区域' },
    dev_proj_approve_date: { type: 'date', required: false, comment: '研发项目立项日期' },
    region_info_province: { type: 'string', maxLength: 100, required: false, comment: '项目区域-省' },
    region_info_city: { type: 'string', maxLength: 100, required: false, comment: '项目区域-市' }
  }
};
```

### 4.计划管理
```javascript
// 计划管理表数据结构
const PlanManagementSchema = {
  tableName: 'model_56pr8d4mrlm',
  comment: '计划管理',
  fields: {
    emp_no: { type: 'string', maxLength: 100, required: false, comment: '员工编号' },
    emp_type: { type: 'string', maxLength: 100, required: false, comment: '员工类型' },
    deputy_dept_id: { type: 'string', maxLength: 100, required: false, comment: '副总级部门id' },
    deputy_dept_name: { type: 'string', maxLength: 100, required: false, comment: '副总级部门名称' },
    deleter_name: { type: 'string', maxLength: 100, required: false, comment: '删除者' },
    updater_name: { type: 'string', maxLength: 100, required: false, comment: '修改者' },
    creator_name: { type: 'string', maxLength: 100, required: false, comment: '创建者' },
    creator_unit: { type: 'text', required: false, comment: '创建者所属部门' },
    is_deleted: { type: 'boolean', required: false, comment: '是否删除' },
    deleter_id: { type: 'string', maxLength: 100, required: false, comment: '删除者ID' },
    updater_id: { type: 'string', maxLength: 100, required: false, comment: '修改者ID' },
    creator_id: { type: 'string', maxLength: 100, required: false, comment: '创建者ID' },
    delete_time: { type: 'datetime', required: false, comment: '删除时间' },
    update_time: { type: 'datetime', required: false, comment: '修改时间' },
    creator_unit_id: { type: 'string', maxLength: 100, required: false, comment: '创建者所属部门ID' },
    create_time: { type: 'datetime', required: false, comment: '创建时间' },
    remark: { type: 'text', required: false, comment: '备注' },
    cid: { type: 'string', maxLength: 100, required: false, comment: '关联id' },
    id: { type: 'string', maxLength: 100, required: true, comment: 'ID', primaryKey: true },
    plan_info: { type: 'string', maxLength: 100, required: false, comment: '计划明细' }
  }
};
```

### 5.计划明细
```javascript
// 计划明细表数据结构
const PlanDetailSchema = {
  tableName: 'model_56prekp6fve',
  comment: '计划明细',
  fields: {
    job_content: { type: 'string', maxLength: 2000, required: false, comment: '工作内容' },
    status: { type: 'string', maxLength: 100, required: false, comment: '计划状态' },
    schedule: { type: 'number', required: false, comment: '当前进度' },
    actual_man_hour: { type: 'number', required: false, comment: '实际工时' },
    estimate_man_hour: { type: 'number', required: false, comment: '预估工时' },
    end_date: { type: 'string', maxLength: 100, required: false, comment: '实际结束日期' },
    begin_date: { type: 'string', maxLength: 100, required: false, comment: '实际开始日期' },
    plan_end_date: { type: 'string', maxLength: 100, required: false, comment: '计划结束日期' },
    plan_begin_date: { type: 'string', maxLength: 100, required: false, comment: '计划开始日期' },
    work_item: { type: 'string', maxLength: 100, required: false, comment: '工作项' },
    job_name: { type: 'string', maxLength: 100, required: false, comment: '产品名称' },
    job_id: { type: 'string', maxLength: 100, required: false, comment: '产品' },
    dev_proj_name: { type: 'string', maxLength: 100, required: false, comment: '研发项目名称' },
    dev_proj_id: { type: 'string', maxLength: 100, required: false, comment: '研发项目' },
    plan_id: { type: 'string', maxLength: 100, required: false, comment: '所属计划' },
    deleter_name: { type: 'string', maxLength: 100, required: false, comment: '删除者' },
    updater_name: { type: 'string', maxLength: 100, required: false, comment: '修改者' },
    creator_name: { type: 'string', maxLength: 100, required: false, comment: '创建者' },
    creator_unit: { type: 'text', required: false, comment: '创建者所属部门' },
    is_deleted: { type: 'boolean', required: false, comment: '是否删除' },
    deleter_id: { type: 'string', maxLength: 100, required: false, comment: '删除者ID' },
    updater_id: { type: 'string', maxLength: 100, required: false, comment: '修改者ID' },
    creator_id: { type: 'string', maxLength: 100, required: false, comment: '创建者ID' },
    delete_time: { type: 'datetime', required: false, comment: '删除时间' },
    update_time: { type: 'datetime', required: false, comment: '修改时间' },
    creator_unit_id: { type: 'string', maxLength: 100, required: false, comment: '创建者所属部门ID' },
    create_time: { type: 'datetime', required: false, comment: '创建时间' },
    remark: { type: 'text', required: false, comment: '备注' },
    cid: { type: 'string', maxLength: 100, required: false, comment: '关联id' },
    id: { type: 'string', maxLength: 100, required: true, comment: 'ID', primaryKey: true }
  }
};
```

### 6.研发项目产品关系表
```javascript
// 研发项目产品关系表数据结构
const DevProjectProductRelationSchema = {
  tableName: 'model_4ldfovr6xnn',
  comment: '研发项目-产品-工作',
  fields: {
    dev_proj_no: { type: 'string', maxLength: 100, required: false, comment: '研发项目编号' },
    dev_proj_id: { type: 'string', maxLength: 100, required: false, comment: '研发项目' },
    job_id: { type: 'string', maxLength: 100, required: false, comment: '产品-工作项' },
    job_name: { type: 'string', maxLength: 100, required: true, comment: '产品-工作项-名称' },
    deleter_name: { type: 'string', maxLength: 100, required: false, comment: '删除者' },
    updater_name: { type: 'string', maxLength: 100, required: false, comment: '修改者' },
    creator_name: { type: 'string', maxLength: 100, required: false, comment: '创建者' },
    business_status: { type: 'number', required: false, comment: '业务件状态' },
    creator_unit: { type: 'text', required: false, comment: '创建者所属部门' },
    bus_type: { type: 'string', maxLength: 100, required: false, comment: '业务类型' },
    bus_type_name: { type: 'string', maxLength: 100, required: false, comment: '业务类型描述' },
    bus_doing: { type: 'string', maxLength: 100, required: false, comment: '在办业务' },
    bus_doing_name: { type: 'string', maxLength: 100, required: false, comment: '在办业务描述' },
    failure: { type: 'number', required: false, comment: '失效状态' },
    failure_name: { type: 'string', maxLength: 100, required: false, comment: '失效状态描述' },
    lastId: { type: 'string', maxLength: 100, required: false, comment: '上手ID' },
    business_status_name: { type: 'string', maxLength: 100, required: false, comment: '业务件状态描述' },
    is_deleted: { type: 'boolean', required: false, comment: '是否删除' },
    deleter_id: { type: 'string', maxLength: 100, required: false, comment: '删除者ID' },
    updater_id: { type: 'string', maxLength: 100, required: false, comment: '修改者ID' },
    creator_id: { type: 'string', maxLength: 100, required: false, comment: '创建者ID' },
    delete_time: { type: 'datetime', required: false, comment: '删除时间' },
    update_time: { type: 'datetime', required: false, comment: '修改时间' },
    failure_time: { type: 'datetime', required: false, comment: '失效时间' },
    creator_unit_id: { type: 'string', maxLength: 100, required: false, comment: '创建者所属部门ID' },
    create_time: { type: 'datetime', required: false, comment: '创建时间' },
    remark: { type: 'string', maxLength: 100, required: false, comment: '备注' },
    business_id: { type: 'string', maxLength: 100, required: false, comment: '业务件id' },
    cid: { type: 'string', maxLength: 100, required: false, comment: '关联id' },
    id: { type: 'string', maxLength: 100, required: true, comment: 'ID', primaryKey: true }
  }
};
```

### 7.产品信息表
```javascript
// 产品信息表数据结构
const ProductInfoSchema = {
  tableName: 'model_4l0zekie1tv',
  comment: '产品（系统/工作）',
  fields: {
    id: { type: 'string', maxLength: 100, required: true, comment: 'ID', primaryKey: true },
    ver_no: { type: 'string', maxLength: 50, required: false, comment: '版本' },
    job_name: { type: 'string', maxLength: 100, required: true, default: 'null', comment: '产品名称' },
    deleter_name: { type: 'string', maxLength: 100, required: false, comment: '删除者' },
    updater_name: { type: 'string', maxLength: 100, required: false, comment: '修改者' },
    creator_name: { type: 'string', maxLength: 100, required: false, comment: '创建者' },
    business_status: { type: 'number', required: false, comment: '业务件状态' },
    is_deleted: { type: 'boolean', required: false, comment: '是否删除' },
    deleter_id: { type: 'string', maxLength: 100, required: false, comment: '删除者ID' },
    updater_id: { type: 'string', maxLength: 100, required: false, comment: '修改者ID' },
    creator_id: { type: 'string', maxLength: 100, required: false, comment: '创建者ID' },
    delete_time: { type: 'datetime', required: false, comment: '删除时间' },
    update_time: { type: 'datetime', required: false, comment: '修改时间' },
    create_time: { type: 'datetime', required: false, comment: '创建时间' },
    remark: { type: 'text', required: false, comment: '备注' },
    business_id: { type: 'string', maxLength: 100, required: false, comment: '业务件id' },
    cid: { type: 'string', maxLength: 100, required: false, comment: '关联id' },
    job_director: { type: 'text', required: false, comment: '负责人' },
    creator_unit: { type: 'text', required: false, comment: '所属系统' },
    bus_type: { type: 'string', maxLength: 200, required: false, comment: '业务类型' },
    bus_doing: { type: 'string', maxLength: 200, required: false, comment: '在办业务' },
    failure: { type: 'number', required: false, comment: '失效状态' },
    lastId: { type: 'string', maxLength: 200, required: false, comment: '上手ID' },
    bus_type_name: { type: 'string', maxLength: 200, required: false, comment: '业务类型描述' },
    bus_doing_name: { type: 'string', maxLength: 200, required: false, comment: '在办业务描述' },
    failure_name: { type: 'string', maxLength: 200, required: false, comment: '失效状态描述' },
    business_status_name: { type: 'string', maxLength: 200, required: false, comment: '业务件状态描述' },
    pro_type: { type: 'string', maxLength: 100, required: false, comment: '类型' },
    pro_line: { type: 'string', maxLength: 100, required: false, comment: '产品线' },
    pro_state: { type: 'string', maxLength: 100, required: false, comment: '状态' },
    pro_basis_platform: { type: 'string', maxLength: 100, required: false, comment: '基础平台' },
    pro_manager: { type: 'text', required: false, comment: '产品经理' },
    pro_department: { type: 'text', required: false, comment: '所属部门' },
    failure_time: { type: 'datetime', required: false, comment: '失效时间' },
    creator_unit_id: { type: 'string', maxLength: 200, required: false, comment: '创建者所属部门ID' },
    pro_no: { type: 'string', maxLength: 100, required: false, comment: '产品编号' },
    test_engineer: { type: 'text', required: false, comment: '测试工程师' }
  }
};
```

### 8.区域信息表（仅用于区分严选项目所属的业务区域）
```javascript
// 区域信息表数据结构
const RegionInfoSchema = {
  tableName: 'model_4l6wlsfzsxx',
  comment: '区域',
  fields: {
    remarks: { type: 'string', maxLength: 100, required: false, comment: '区域说明' },
    region_name: { type: 'string', maxLength: 50, required: false, comment: '区域名称' },
    deleter_name: { type: 'string', maxLength: 100, required: false, comment: '删除者' },
    updater_name: { type: 'string', maxLength: 100, required: false, comment: '修改者' },
    creator_name: { type: 'string', maxLength: 100, required: false, comment: '创建者' },
    business_status: { type: 'number', required: false, comment: '业务件状态' },
    is_deleted: { type: 'boolean', required: false, comment: '是否删除' },
    deleter_id: { type: 'string', maxLength: 100, required: false, comment: '删除者ID' },
    updater_id: { type: 'string', maxLength: 100, required: false, comment: '修改者ID' },
    creator_id: { type: 'string', maxLength: 100, required: false, comment: '创建者ID' },
    delete_time: { type: 'datetime', required: false, comment: '删除时间' },
    update_time: { type: 'datetime', required: false, comment: '修改时间' },
    create_time: { type: 'datetime', required: false, comment: '创建时间' },
    remark: { type: 'text', required: false, comment: '备注' },
    business_id: { type: 'string', maxLength: 100, required: false, comment: '业务件id' },
    cid: { type: 'string', maxLength: 100, required: false, comment: '关联id' },
    id: { type: 'string', maxLength: 100, required: true, comment: 'ID', primaryKey: true },
    creator_unit: { type: 'text', required: false, comment: '所属部门' },
    bus_type: { type: 'string', maxLength: 200, required: false, comment: '业务类型' },
    bus_doing: { type: 'string', maxLength: 200, required: false, comment: '在办业务' },
    failure: { type: 'number', required: false, comment: '失效状态' },
    lastId: { type: 'string', maxLength: 200, required: false, comment: '上手ID' },
    bus_type_name: { type: 'string', maxLength: 200, required: false, comment: '业务类型描述' },
    bus_doing_name: { type: 'string', maxLength: 200, required: false, comment: '在办业务描述' },
    failure_name: { type: 'string', maxLength: 200, required: false, comment: '失效状态描述' },
    business_status_name: { type: 'string', maxLength: 200, required: false, comment: '业务件状态描述' },
    failure_time: { type: 'datetime', required: false, comment: '失效时间' },
    creator_unit_id: { type: 'string', maxLength: 200, required: false, comment: '创建者所属部门ID' }
  }
};
```


## Api
### 获取日报数据列表：
```javascript
url：https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/model/list
method：post
parames：{"pageSize":10,"pageNo":1,"pageIndex":1,"page_id":"785761401783296","extendData":{},"tabType":"model","model_id":"647a312d-ce90-4968-b159-5b531f85bc53","sort":[{"key":"create_time","mode":"desc"}],"component_id":"16cf3d7c-a537-1ec2-a69f-93e282ece000","params":[{"logicalOperator":"","conditions":[{"key":"is_deleted","operator":"EQUAL","value":"0","logicalOperator":""}]}],"joinFields":["daily_info"],"filter":[]}
response：{
    "status": 200,
    "data": {
        "list": [
            {
                "first_dept_id": "eeb003cd-52a2-4963-90cf-29e3dbe975da",
                "first_dept_name": "用户体验部",
                "deputy_dept_id": "0629d0d2-6e39-461e-acc4-00f153c41c0a",
                "deputy_dept_name": "技术研发中心",
                "emp_no": "ZH000123",
                "emp_type": "1",
                "emp_dept": "用户体验部",
                "daily_date": "2025-07-22",
                "daily_titile": "罗前2025-07-22",
                "deleter_name": null,
                "updater_name": "张竞",
                "creator_name": "罗前",
                "business_status": 3,
                "creator_unit": "{\"id\":\"eeb003cd-52a2-4963-90cf-29e3dbe975da\",\"name\":\"用户体验部\",\"code\":\"836756197\",\"level\":2,\"socialCreditCode\":null,\"extendParams\":null,\"isMainUnit\":false,\"enterpriseType\":null}",
                "bus_type": "ADD",
                "bus_type_name": "新建",
                "bus_doing": null,
                "bus_doing_name": null,
                "failure": 0,
                "failure_name": "有效",
                "lastId": null,
                "business_status_name": "审核通过",
                "is_deleted": false,
                "deleter_id": null,
                "updater_id": "7adb824c-afee-4e01-946a-e4582a4026e2",
                "creator_id": "ee7a8b8a-1100-44a9-819f-9dd25404c06b",
                "delete_time": null,
                "update_time": "2025-07-22 18:58:52",
                "failure_time": null,
                "creator_unit_id": "eeb003cd-52a2-4963-90cf-29e3dbe975da",
                "create_time": "2025-07-22 17:07:47",
                "remark": null,
                "business_id": "202507220000135",
                "cid": "****************",
                "id": "6529084391650305",
                "daily_info": {
                    "deputy_dept_name": "技术研发中心",
                    "post_name": "管理岗",
                    "dev_proj_dep_name": "平台产研部",
                    "dev_proj_type": "pro",
                    "dev_proj_job_name": "数飞基础平台",
                    "dev_proj_id": {
                        "region_info_city": "成都市",
                        "region_info_province": "四川省",
                        "dev_proj_approve_date": null,
                        "region_info": "{\"province\":{\"value\":\"510000\",\"label\":\"四川省\"},\"city\":{\"value\":\"510100\",\"label\":\"成都市\"},\"district\":{},\"community\":{},\"street\":{},\"addressFull\":\"\",\"location\":[],\"other\":\"\"}",
                        "dev_proj_add_imm": null,
                        "dev_proj_end_cause": null,
                        "funi_proj_sn": "FLYM2024-YFZ144",
                        "dev_proj_pause_cause": null,
                        "dev_proj_add_cost": null,
                        "dev_proj_into_off_date": null,
                        "dev_proj_bus_dep_pm": "[{\"type\":\"user\",\"id\":\"7adb824c-afee-4e01-946a-e4582a4026e2\",\"name\":\"张竞\"}]",
                        "pro_info": null,
                        "dev_proj_bus_dep": "[{\"id\":\"0629d0d2-6e39-461e-acc4-00f153c41c0a\",\"name\":\"技术研发中心\",\"type\":\"org\"}]",
                        "dev_proj_change_cause": null,
                        "dev_proj_merit_coefficient": null,
                        "dev_proj_degree": null,
                        "dev_proj_complexity": null,
                        "region_name": "不限",
                        "region_id": "1245479229652993",
                        "dev_proj_type": "pro",
                        "funi_proj_name": "数飞平台三期",
                        "dev_proj_terminate_time": null,
                        "dev_proj_pdsp_time": null,
                        "dev_proj_stage": "3",
                        "dev_proj_income_exp": "研发投入项目，无收入预估",
                        "dev_proj_imm": 160,
                        "dept_name": "技术研发中心",
                        "dept_id": "0629d0d2-6e39-461e-acc4-00f153c41c0a",
                        "dev_proj_dep": "[{\"type\":\"org\",\"id\":\"8e91abf5-3ced-4635-b57c-67b381d35fed\",\"name\":\"平台产研部\"}]",
                        "funi_proj_stakeholders": "安宗源，陈维流，罗前，罗林，谢进，马骏瑀，梁良，皮敏，林焕，郑佳，孟雪峰，古加文，田芮宇，魏雄峰，彭毅，唐鹏，王金辉，祝昌节，李阳，杨涛，邹国平，笪丽芳，谭天顺，仲彦兵，王武，蒋维明，邹彭强",
                        "dev_proj_pm_phone": "18982288813",
                        "dev_proj_pm": "[{\"type\":\"user\",\"id\":\"da40839a-9c41-4885-beaa-306c99c03423\",\"name\":\"安宗源\"}]",
                        "funi_proj_target": "平台将作为行业软件的基础平台，为公司的住建项目提供强有力的支撑，涵盖了金融服务、房产政务、数据工程等多个关键领域。",
                        "funi_proj_scope": "基础操作平台、接口总线、业务中台、数搭低代码平台、数据可视化平台、数据服务平台、资金中台",
                        "funi_proj_income": 0,
                        "dev_proj_cost": 2560000,
                        "funi_proj_end_date": "2025-12-30",
                        "funi_proj_begin_date": "2025-01-02",
                        "dev_proj_no": "FLYM2025-YF012",
                        "dev_proj_sys_info": null,
                        "funi_proj_id": "756f80b0-b644-4975-903a-371501e70fdc",
                        "dev_proj_status": "in_prog",
                        "dev_proj_ailas": null,
                        "region_code": null,
                        "dev_proj_name": "数飞平台三期",
                        "deleter_name": null,
                        "updater_name": "罗前",
                        "creator_name": "安宗源",
                        "creator_unit": "{\"id\":\"8e91abf5-3ced-4635-b57c-67b381d35fed\",\"name\":\"平台产研部\",\"code\":\"87602060\",\"socialCreditCode\":null,\"extendParams\":null,\"isMainUnit\":false}",
                        "is_deleted": false,
                        "deleter_id": null,
                        "updater_id": "ee7a8b8a-1100-44a9-819f-9dd25404c06b",
                        "creator_id": "da40839a-9c41-4885-beaa-306c99c03423",
                        "delete_time": null,
                        "update_time": "2025-07-04 17:24:33",
                        "creator_unit_id": "8e91abf5-3ced-4635-b57c-67b381d35fed",
                        "create_time": "2025-01-03 15:45:03",
                        "remark": null,
                        "cid": "***************",
                        "id": "***************"
                    },
                    "region_name": "不限",
                    "region_id": {
                        "remarks": null,
                        "region_name": "不限",
                        "deleter_name": null,
                        "updater_name": "皮敏",
                        "creator_name": "皮敏",
                        "creator_unit": "{\"id\":\"1000d7ab-17f6-42a8-ab25-027e9d0ff8b0\",\"name\":\"项目质量部\",\"code\":\"645650281\",\"isMainUnit\":false}",
                        "is_deleted": false,
                        "deleter_id": null,
                        "updater_id": "5735ac65-5e7e-4ea7-b9d2-3cd6179cc968",
                        "creator_id": "5735ac65-5e7e-4ea7-b9d2-3cd6179cc968",
                        "delete_time": null,
                        "update_time": "2024-04-07 18:21:38",
                        "creator_unit_id": null,
                        "create_time": "2024-04-07 18:21:38",
                        "remark": null,
                        "cid": "1245479229652992",
                        "id": "1245479229652993"
                    },
                    "dev_proj_job_id": {
                        "dev_proj_no": "FLYM2025-YF012",
                        "dev_proj_id": "***************",
                        "job_id": "f555c8d4-fc99-11ee-aef1-043f72fdeca8",
                        "job_name": "数飞基础平台",
                        "deleter_name": null,
                        "updater_name": "皮敏",
                        "creator_name": "皮敏",
                        "creator_unit": "{\"id\":\"1000d7ab-17f6-42a8-ab25-027e9d0ff8b0\",\"name\":\"项目质量部\",\"code\":\"645650281\",\"socialCreditCode\":null,\"extendParams\":null,\"isMainUnit\":false}",
                        "is_deleted": false,
                        "deleter_id": null,
                        "updater_id": "5735ac65-5e7e-4ea7-b9d2-3cd6179cc968",
                        "creator_id": "5735ac65-5e7e-4ea7-b9d2-3cd6179cc968",
                        "delete_time": null,
                        "update_time": "2025-07-04 15:03:38",
                        "creator_unit_id": "1000d7ab-17f6-42a8-ab25-027e9d0ff8b0",
                        "create_time": "2025-01-08 12:13:13",
                        "remark": null,
                        "cid": "***************",
                        "id": "8565185599202590720"
                    },
                    "job_content": "业务系统使用ai自动化测试方案研究：基于PRD的测试用例生成提示词+ PlaywrightMCP实现",
                    "days_invested": 1,
                    "dev_proj_name": "数飞平台三期",
                    "dev_proj_no": "FLYM2025-YF012",
                    "deleter_name": null,
                    "updater_name": null,
                    "creator_name": "罗前",
                    "creator_unit": "{\"id\":\"eeb003cd-52a2-4963-90cf-29e3dbe975da\",\"name\":\"用户体验部\",\"code\":\"836756197\",\"level\":2,\"socialCreditCode\":null,\"extendParams\":null,\"isMainUnit\":false,\"enterpriseType\":null}",
                    "is_deleted": false,
                    "deleter_id": null,
                    "updater_id": null,
                    "creator_id": "ee7a8b8a-1100-44a9-819f-9dd25404c06b",
                    "delete_time": null,
                    "update_time": null,
                    "creator_unit_id": "eeb003cd-52a2-4963-90cf-29e3dbe975da",
                    "create_time": "2025-07-22 17:07:47",
                    "remark": null,
                    "cid": "****************",
                    "id": "8635925364046233600"
                },
                "activityId": "",
                "activityName": ""
            }
        ],
        "total": 1
    },
    "success": true
}
```

### 获取计划数据列表：
```javascript
url：https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/model/list
method：post
parames：{"pageSize":10,"pageNo":1,"pageIndex":1,"page_id":"12734221815051265","extendData":{},"tabType":"model","model_id":"7fd9e661-8a61-451a-a9aa-7d8ff21aecb7","sort":[],"component_id":"4171130f-b0af-5a6c-f09f-acf14502051b","params":[{"logicalOperator":"","conditions":[{"key":"is_deleted","operator":"EQUAL","value":"0","logicalOperator":""}]}],"joinFields":["plan_info"],"filter":[]}
response：{
    "status": 200,
    "data": {
        "list": [
            {
                "emp_no": "ZH000123",
                "emp_type": "1",
                "deputy_dept_id": "0629d0d2-6e39-461e-acc4-00f153c41c0a",
                "deputy_dept_name": "技术研发中心",
                "deleter_name": null,
                "updater_name": "罗前",
                "creator_name": "罗前",
                "creator_unit": "用户体验部",
                "is_deleted": false,
                "deleter_id": null,
                "updater_id": "ee7a8b8a-1100-44a9-819f-9dd25404c06b",
                "creator_id": "ee7a8b8a-1100-44a9-819f-9dd25404c06b",
                "delete_time": null,
                "update_time": "2025-07-05 11:36:34",
                "creator_unit_id": "eeb003cd-52a2-4963-90cf-29e3dbe975da",
                "create_time": "2025-06-20 14:15:16",
                "remark": null,
                "cid": "13393842423791616",
                "id": "13393842423791617",
                "plan_info": {
                    "job_content": "test1",
                    "status": null,
                    "schedule": null,
                    "actual_man_hour": null,
                    "estimate_man_hour": 8,
                    "end_date": null,
                    "begin_date": null,
                    "plan_end_date": "2025-06-27",
                    "plan_begin_date": "2025-06-10",
                    "work_item": "功能研发",
                    "job_name": "住房交易服务平台",
                    "job_id": {
                        "test_engineer": null,
                        "pro_no": null,
                        "pro_department": null,
                        "pro_manager": null,
                        "pro_basis_platform": null,
                        "pro_state": null,
                        "pro_line": null,
                        "pro_type": null,
                        "job_director": null,
                        "ver_no": null,
                        "job_name": null,
                        "deleter_name": null,
                        "updater_name": null,
                        "creator_name": null,
                        "creator_unit": null,
                        "is_deleted": null,
                        "deleter_id": null,
                        "updater_id": null,
                        "creator_id": null,
                        "delete_time": null,
                        "update_time": null,
                        "creator_unit_id": null,
                        "create_time": null,
                        "remark": null,
                        "cid": null,
                        "id": null
                    },
                    "dev_proj_name": "“宜居凉都·房产超市”线上系统平台技术服务",
                    "dev_proj_id": {
                        "region_info_city": "六盘水市",
                        "region_info_province": "贵州省",
                        "dev_proj_approve_date": null,
                        "region_info": "{\"province\":{\"value\":\"520000\",\"label\":\"贵州省\"},\"city\":{\"value\":\"520200\",\"label\":\"六盘水市\"},\"district\":{},\"community\":{},\"street\":{},\"addressFull\":\"\",\"location\":[],\"other\":\"\"}",
                        "dev_proj_add_imm": 0,
                        "dev_proj_end_cause": null,
                        "funi_proj_sn": "FLYM2025-JY013",
                        "dev_proj_pause_cause": null,
                        "dev_proj_add_cost": null,
                        "dev_proj_into_off_date": null,
                        "dev_proj_bus_dep_pm": "[{\"type\":\"user\",\"id\":\"2903185c-383f-4cba-8d70-464423a78ecd\",\"name\":\"舒稚寒\"}]",
                        "pro_info": null,
                        "dev_proj_bus_dep": "[{\"id\":\"172d85db-a0cd-4d07-8f7e-93a3f8311309\",\"name\":\"新业务事业群\",\"type\":\"org\"}]",
                        "dev_proj_change_cause": null,
                        "dev_proj_merit_coefficient": null,
                        "dev_proj_degree": null,
                        "dev_proj_complexity": null,
                        "region_name": "贵州",
                        "region_id": "14242187576435713",
                        "dev_proj_type": "imp",
                        "funi_proj_name": "“宜居凉都·房产超市”线上系统平台技术服务",
                        "dev_proj_terminate_time": null,
                        "dev_proj_pdsp_time": null,
                        "dev_proj_stage": "3",
                        "dev_proj_income_exp": "三年服务期，含政府侧收入+银行端收入，预计222万元。",
                        "dev_proj_imm": 70.3,
                        "dept_name": "新业务事业群",
                        "dept_id": "172d85db-a0cd-4d07-8f7e-93a3f8311309",
                        "dev_proj_dep": "[{\"type\":\"org\",\"id\":\"053700e1-5c33-439e-ae88-5e9b130c8dc0\",\"name\":\"增值产研部\"}]",
                        "funi_proj_stakeholders": "钟玉浪、姜瀚、夏杰丽、唐鹏、姜维明、林鹏、向锐、古嘉文、周杰、周杨、周碧海、笪丽芳、邓军、田凤雪、王金辉、李金英、周欣",
                        "dev_proj_pm_phone": "15828113115",
                        "dev_proj_pm": "[{\"type\":\"user\",\"id\":\"82158d1b-1c74-48cd-bd72-b5e2111d946a\",\"name\":\"钟玉浪\"}]",
                        "funi_proj_target": "2025年6月初完成宜居凉都·房产超市上线实施",
                        "funi_proj_scope": "乐居平台、bpaas，交易生态4.0存量房板块（存量房网签系统、存量房资金监管系统、从业主体管理系统、基础平台）",
                        "funi_proj_income": 2220000,
                        "dev_proj_cost": 1124800,
                        "funi_proj_end_date": "2028-08-31",
                        "funi_proj_begin_date": "2025-04-17",
                        "dev_proj_no": "FLYM2025-YF035",
                        "dev_proj_sys_info": null,
                        "funi_proj_id": "0d9d53d0-7e0b-4aaf-9b8f-63c144718430",
                        "dev_proj_status": "in_prog",
                        "dev_proj_ailas": null,
                        "region_code": null,
                        "dev_proj_name": "“宜居凉都·房产超市”线上系统平台技术服务",
                        "deleter_name": null,
                        "updater_name": "皮敏",
                        "creator_name": "钟玉浪",
                        "creator_unit": "{\"id\":\"886d900a-7a54-4dba-be08-6b1d331aff52\",\"name\":\"交易产研部\",\"code\":\"87706084\",\"level\":2,\"socialCreditCode\":null,\"extendParams\":null,\"isMainUnit\":false}",
                        "is_deleted": false,
                        "deleter_id": null,
                        "updater_id": "5735ac65-5e7e-4ea7-b9d2-3cd6179cc968",
                        "creator_id": "82158d1b-1c74-48cd-bd72-b5e2111d946a",
                        "delete_time": null,
                        "update_time": "2025-07-15 14:21:35",
                        "creator_unit_id": "886d900a-7a54-4dba-be08-6b1d331aff52",
                        "create_time": "2025-04-24 10:53:03",
                        "remark": null,
                        "cid": "15202588487050240",
                        "id": "15202588487050241"
                    },
                    "deleter_name": null,
                    "updater_name": "罗前",
                    "creator_name": "罗前",
                    "creator_unit": "{\"id\":\"eeb003cd-52a2-4963-90cf-29e3dbe975da\",\"name\":\"用户体验部\",\"code\":\"836756197\",\"level\":2,\"socialCreditCode\":null,\"extendParams\":null,\"isMainUnit\":false}",
                    "is_deleted": false,
                    "deleter_id": null,
                    "updater_id": "ee7a8b8a-1100-44a9-819f-9dd25404c06b",
                    "creator_id": "ee7a8b8a-1100-44a9-819f-9dd25404c06b",
                    "delete_time": null,
                    "update_time": "2025-07-05 11:36:34",
                    "creator_unit_id": "eeb003cd-52a2-4963-90cf-29e3dbe975da",
                    "create_time": "2025-06-20 14:15:16",
                    "remark": null,
                    "cid": "13393842423791616",
                    "id": "8624285536460406784"
                }
            }
        ],
        "total": 1
    },
    "success": true
}
```

### 获取研发项目数据列表：
```javascript
url：https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/model/list
method：post
parames：{"pageSize":10,"pageNo":1,"pageIndex":1,"page_id":"****************","extendData":{},"tabType":"model","model_id":"ad96ce63-35b0-4c0d-b2e6-42c9f8a793d8","sort":[{"serialNumber":1,"uuid":"ff0ca1a2-2f61-95a9-cbb0-582fe0156533","key":"dev_proj_no","mode":"desc"}],"component_id":"c2264b60-870f-220c-206b-e39ea1e97b4c","params":[{"logicalOperator":"","conditions":[{"key":"dept_id","operator":"LIKE","value":["17f0b34e-e41a-4782-8fbb-488874fbe7cc","eeb003cd-52a2-4963-90cf-29e3dbe975da"],"logicalOperator":"AND"},{"key":"is_deleted","operator":"EQUAL","value":"0","logicalOperator":"AND"}]},{"logicalOperator":"OR","conditions":[{"key":"is_deleted","operator":"EQUAL","value":"0","logicalOperator":""},{"key":"dev_proj_pm","operator":"LIKE","value":"ee7a8b8a-1100-44a9-819f-9dd25404c06b","logicalOperator":"AND"}]},{"logicalOperator":"OR","conditions":[{"key":"is_deleted","operator":"EQUAL","value":"0","logicalOperator":""},{"key":"bus_doing","operator":"IS_NULL","logicalOperator":"AND"}]}],"joinFields":[],"filter":[]}
response：{
    "status": 200,
    "data": {
        "list": [
            {
                "region_info_city": "成都市",
                "region_info_province": "四川省",
                "dev_proj_approve_date": "2025-07-24",
                "region_info": "{\"province\":{\"value\":\"510000\",\"label\":\"四川省\"},\"city\":{\"value\":\"510100\",\"label\":\"成都市\"},\"district\":{},\"community\":{},\"street\":{},\"addressFull\":\"\",\"location\":[],\"other\":\"\"}",
                "dev_proj_add_imm": 0,
                "dev_proj_end_cause": null,
                "funi_proj_sn": "FLYM2025-JY022",
                "dev_proj_pause_cause": null,
                "dev_proj_add_cost": null,
                "dev_proj_into_off_date": null,
                "dev_proj_bus_dep_pm": "[{\"type\":\"user\",\"id\":\"1b86cbe7-86cd-44a8-999c-3bc5a71db67e\",\"name\":\"谭雪琴\"}]",
                "dev_proj_bus_dep": "大西区业务部",
                "dev_proj_change_cause": null,
                "dev_proj_merit_coefficient": null,
                "dev_proj_degree": null,
                "dev_proj_complexity": null,
                "region_name": "成都（其它）",
                "region_id": "392486441517057",
                "dev_proj_type": "ops",
                "funi_proj_name": "智慧蓉城值班值守系统运维项目",
                "dev_proj_terminate_time": null,
                "dev_proj_pdsp_time": null,
                "dev_proj_stage": "2",
                "dev_proj_income_exp": "为成都市智慧蓉城值班值守系统提供系统运维服务，收取运维费",
                "dev_proj_imm": 24,
                "dept_name": "大西区业务部",
                "dept_id": "1e6eb2d3-9da5-4da6-afb2-9be4b329be44",
                "dev_proj_dep": "技术研发中心",
                "funi_proj_stakeholders": null,
                "dev_proj_pm_phone": null,
                "dev_proj_pm": null,
                "funi_proj_target": null,
                "funi_proj_scope": "为成都市智慧蓉城值班值守系统提供系统运维服务",
                "funi_proj_income": 780000,
                "dev_proj_cost": 432000,
                "funi_proj_end_date": "2025-11-30",
                "funi_proj_begin_date": "2025-05-19",
                "dev_proj_no": "FLYM2025-YF120",
                "funi_proj_id": "c53a6972-eb66-4170-9cb3-d5bead11654a",
                "dev_proj_status": "in_prog",
                "dev_proj_ailas": null,
                "region_code": null,
                "dev_proj_name": "智慧蓉城值班值守系统运维项目",
                "deleter_name": null,
                "updater_name": "皮敏",
                "creator_name": "张竞",
                "business_status": 3,
                "creator_unit": "{\"id\":\"0629d0d2-6e39-461e-acc4-00f153c41c0a\",\"name\":\"技术研发中心\",\"code\":\"87645074\",\"level\":1,\"socialCreditCode\":null,\"extendParams\":null,\"isMainUnit\":false}",
                "bus_type": "ADD",
                "bus_type_name": "新建",
                "bus_doing": null,
                "bus_doing_name": null,
                "failure": 0,
                "failure_name": "有效",
                "lastId": null,
                "business_status_name": "审核通过",
                "is_deleted": false,
                "deleter_id": null,
                "updater_id": "5735ac65-5e7e-4ea7-b9d2-3cd6179cc968",
                "creator_id": "7adb824c-afee-4e01-946a-e4582a4026e2",
                "delete_time": null,
                "update_time": "2025-07-24 20:58:25",
                "failure_time": null,
                "creator_unit_id": "0629d0d2-6e39-461e-acc4-00f153c41c0a",
                "create_time": "2025-07-10 20:42:40",
                "remark": null,
                "business_id": "202507100000373",
                "cid": "2234507742084096",
                "id": "2234507742084097",
                "activityId": "",
                "activityName": ""
            }
        ],
        "total": 279
    },
    "success": true
}
```
### 获取区域数据列表：
```javascript
url：https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/model/list
method：post
parames：{"pageSize":10,"pageNo":1,"pageIndex":1,"page_id":"7537538279425","tabType":"model","model_id":"8edda8a6-69f3-44c7-856f-66c4116d47c1","sort":[],"component_id":"132e52b7-2232-e78f-0c94-6aebef105338","params":[{"logicalOperator":"","conditions":[{"key":"is_deleted","operator":"EQUAL","value":"0","logicalOperator":""}]}],"joinFields":[],"filter":[]}
response：{
    "status": 200,
    "data": {
        "list": [
            {
                "remarks": "广安",
                "region_name": "广安",
                "deleter_name": null,
                "updater_name": null,
                "creator_name": "皮敏",
                "business_status": null,
                "creator_unit": "{\"id\":\"1000d7ab-17f6-42a8-ab25-027e9d0ff8b0\",\"name\":\"项目质量部\",\"code\":\"645650281\",\"level\":2,\"socialCreditCode\":null,\"extendParams\":null,\"isMainUnit\":false}",
                "bus_type": "ADD",
                "bus_type_name": "新建",
                "bus_doing": null,
                "bus_doing_name": null,
                "failure": null,
                "failure_name": null,
                "lastId": null,
                "business_status_name": null,
                "is_deleted": false,
                "deleter_id": null,
                "updater_id": null,
                "creator_id": "5735ac65-5e7e-4ea7-b9d2-3cd6179cc968",
                "delete_time": null,
                "update_time": null,
                "failure_time": null,
                "creator_unit_id": "1000d7ab-17f6-42a8-ab25-027e9d0ff8b0",
                "create_time": "2025-07-07 18:41:34",
                "remark": null,
                "business_id": null,
                "cid": "1116868423846912",
                "id": "1116868423846913"
            }
        ],
        "total": 30
    },
    "success": true
}

```




## 需求：
<!-- 1. 任务维度：部门下所有人的任务完成情况、进度、是否有风险、以及任务完成情况
2. 人员维度：查看人员在哪些项目中；
3. 项目维度：查看这个项目中投入的人员清单及这个人在这个项目的投入比例（人会同时在多个项目上的总投入是100%， 需要知道在当前项目的投入比例）
4. 人员工作饱和度：查看人员工作饱和度，即一个人在多个项目上的投入比例之和是否超过100%
5. 人员忙闲状态：查看人员在某个时间跨度中具体哪天是否忙碌，即是否有任务，以及任务是否完成，是否空闲
6. 其他需求：根据需求，从部门负责人的视觉添加其他功能 -->

## 技术栈
1. 前端：HTML + Vue3（CDN引入） + ElementPlus（CDN引入） + ECharts/D3.js（CDN引入）
2. 后端：仅提供查询API接口（基于现有接口）
3. 数据处理：前端数据聚合和计算
4. 开发方式：直接HTML页面，无需工程化构建

注意：
所有api请求都需要加上的header：
headers: {
        'Content-Type': 'application/json',
        'x-funipaas-authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJTTTNXaXRoU00yIn0.eyJ1YXRfdWlkIjoiNGZlNGFlNDMwNGQ5OWQyZmE5YWRmZDAwNGY2OWVlNWE5NTkxMDZlYjM5YWM3YzFjZDlkNGY5ODkzZmI2MmEzOTVjYjAyYWFiMzQzODQwNGZiMjE1ZGMwMjMyYzY3MmYxIiwidWF0X3NpZCI6ImJiODNiZWExM2Q3ZWM2YjBiZWNjMWEwNTkzMjZlMDg3YjNkMGMzN2I0NmU0ZWZjNmZlMGZlODc0MWE3Y2IxNDJiYWE4OWMyZjgyNmY3Y2QxZTBkOTg3NGM2MGEwMjhkNzc2YzcyMmExZWRhZTUzNWUzM2JiZDZmNWZmMDQ5OGUwOWViMzc2ODRjOWQ3MDFkODdkMDQwMGZlZjYyY2ZjNzk4ZGRjZjQ3NWYzNTA5MTA3ZWJlZDdkNjI2ZjBlOTEwZTNjYmEyOGJjMGViZGU2Y2M5NDJjYzFjZTBkM2Q5ODkzIiwiZXhwIjoxNzU0MTE5NTIwfQ.MEYCIQDz8CsA8sKxU6tZK8cQBrlSb5MP1eRz5ZFIrJx8FqLi_wIhAPaop-6KyxjNG1BusTzN03_VmfdDdLUNBKKp8eqlRu9B',
        'x-funipaas-request-hash': 'ce3b082aa9783a6bef7c71470699f003bf51c7622b9052554eb4073375f08a20',
        'x-funipaas-request-id': 'f0cc6871-56c0-219e-8f5c-11aa2957a301',
        'x-funipaas-tenant': 'dev'
    }