<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投入分析系统 - 项目维度</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #303133;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .filter-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .overview-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .overview-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .overview-card:hover {
            transform: translateY(-2px);
        }
        
        .card-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .card-icon.projects { color: #409EFF; }
        .card-icon.active { color: #67C23A; }
        .card-icon.completed { color: #E6A23C; }
        .card-icon.investment { color: #F56C6C; }
        
        .card-number {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
        }
        
        .card-label {
            color: #909399;
            font-size: 14px;
        }
        
        .main-content {
            display: grid;
            grid-template-rows: auto 1fr;
            gap: 20px;
        }
        
        .project-list-section {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .analysis-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .chart-panel {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #409EFF;
        }
        
        .project-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .project-table th,
        .project-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .project-table th {
            background-color: #fafafa;
            font-weight: 600;
        }
        
        .project-row {
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .project-row:hover {
            background-color: #f5f7fa;
        }
        
        .selected-project {
            background-color: #ecf5ff !important;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-progress { background-color: #e1f3d8; color: #67c23a; }
        .status-completed { background-color: #fdf6ec; color: #e6a23c; }
        .status-paused { background-color: #fef0f0; color: #f56c6c; }
        
        .progress-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .progress-bar {
            flex: 1;
            height: 8px;
            background-color: #e4e7ed;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #409eff;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            font-size: 12px;
            color: #606266;
            white-space: nowrap;
        }
        
        .chart-box {
            height: 350px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
        }

        .pagination-container {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 20px 0;
            border-top: 1px solid #e4e7ed;
            margin-top: 10px;
            background: #fafafa;
            border-radius: 0 0 8px 8px;
        }

        /* 优化Element Plus分页组件样式 */
        .pagination-container .el-pagination {
            --el-pagination-font-size: 14px;
            --el-pagination-bg-color: transparent;
            --el-pagination-text-color: #606266;
            --el-pagination-border-radius: 6px;
            --el-pagination-button-color: #606266;
            --el-pagination-button-bg-color: #fff;
            --el-pagination-button-disabled-color: #c0c4cc;
            --el-pagination-button-disabled-bg-color: #fff;
            --el-pagination-hover-color: #409eff;
            --el-pagination-active-color: #409eff;
            --el-pagination-active-bg-color: #409eff;
        }

        .pagination-container .el-pagination .el-pager li {
            min-width: 32px;
            height: 32px;
            line-height: 30px;
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            margin: 0 2px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .pagination-container .el-pagination .el-pager li:hover {
            border-color: #409eff;
            color: #409eff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        .pagination-container .el-pagination .el-pager li.is-active {
            background: #409eff;
            border-color: #409eff;
            color: #fff;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }

        .pagination-container .el-pagination .btn-prev,
        .pagination-container .el-pagination .btn-next {
            min-width: 32px;
            height: 32px;
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            margin: 0 2px;
            transition: all 0.2s ease;
        }

        .pagination-container .el-pagination .btn-prev:hover,
        .pagination-container .el-pagination .btn-next:hover {
            border-color: #409eff;
            color: #409eff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        .pagination-container .el-pagination .el-select .el-input {
            width: 80px;
        }

        .pagination-container .el-pagination .el-select .el-input__wrapper {
            border-radius: 6px;
            border: 1px solid #dcdfe6;
            transition: all 0.2s ease;
        }

        .pagination-container .el-pagination .el-select .el-input__wrapper:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        .pagination-container .el-pagination .el-pagination__jump {
            margin-left: 16px;
        }

        .pagination-container .el-pagination .el-pagination__jump .el-input {
            width: 60px;
        }

        .pagination-container .el-pagination .el-pagination__jump .el-input__wrapper {
            border-radius: 6px;
            border: 1px solid #dcdfe6;
            transition: all 0.2s ease;
        }

        .pagination-container .el-pagination .el-pagination__jump .el-input__wrapper:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        .pagination-container .el-pagination .el-pagination__total {
            color: #909399;
            font-weight: 500;
            margin-right: 16px;
        }

        .pagination-container .el-pagination .el-pagination__sizes {
            margin-right: 16px;
        }
        
        .team-badge {
            background-color: #409eff;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 12px;
            margin-right: 5px;
        }
        
        .importance-stars {
            color: #ff9900;
        }
        
        .btn-detail {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 5px;
        }
        
        .btn-analysis {
            background-color: #67c23a;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        
        @media (max-width: 1200px) {
            .overview-cards {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .analysis-section {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .overview-cards {
                grid-template-columns: 1fr;
            }
        }

        .data-indicator {
            display: inline-flex;
            align-items: center;
            margin-left: 8px;
            font-size: 14px;
        }

        .data-real {
            color: #67c23a;
        }

        .data-mock {
            color: #e6a23c;
        }

        .indicator-tooltip {
            margin-left: 4px;
            cursor: help;
        }

        /* 数据标识图标样式 */
        .data-indicator .el-icon-success::before {
            content: "✓";
            font-style: normal;
            font-weight: bold;
        }

        .data-indicator .el-icon-warning::before {
            content: "⚠️";
            font-style: normal;
        }

        /* 排序图标样式 */
        .el-icon-sort::before {
            content: "↕";
            color: #c0c4cc;
        }

        .el-icon-sort-up::before {
            content: "↑";
            color: #409eff;
        }

        .el-icon-sort-down::before {
            content: "↓";
            color: #409eff;
        }

        th[style*="cursor: pointer"]:hover {
            background-color: #f5f7fa;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 15px 0;
            }

            .header h1 {
                font-size: 22px;
                margin-bottom: 8px;
            }

            .header p {
                font-size: 13px;
            }

            .filters {
                flex-direction: column;
                gap: 10px;
                margin-bottom: 20px;
            }

            .filter-group {
                width: 100%;
            }

            .filter-group label {
                font-size: 13px;
                margin-bottom: 5px;
            }

            .overview-cards {
                grid-template-columns: 1fr;
                gap: 15px;
                margin-bottom: 20px;
            }

            .overview-card {
                padding: 15px;
            }

            .overview-card h3 {
                font-size: 14px;
            }

            .overview-card .number {
                font-size: 20px;
            }

            .analysis-section {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .chart-container {
                height: 250px;
            }

            .table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .project-table {
                min-width: 700px;
                font-size: 12px;
            }

            .project-table th,
            .project-table td {
                padding: 8px 4px;
                font-size: 12px;
                white-space: nowrap;
            }

            .project-table th {
                background-color: #f8f9fa;
                position: sticky;
                top: 0;
                z-index: 10;
            }

            .pagination-container {
                justify-content: center;
                margin-top: 15px;
            }

            .el-pagination {
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 20px;
            }

            .chart-container {
                height: 200px;
            }

            .project-table th,
            .project-table td {
                padding: 6px 2px;
                font-size: 11px;
            }

            .el-pagination {
                font-size: 12px;
            }
        }

        /* 排序图标样式 */
        .el-icon-sort::before {
            content: "↕";
            color: #c0c4cc;
        }

        .el-icon-sort-up::before {
            content: "↑";
            color: #409eff;
        }

        .el-icon-sort-down::before {
            content: "↓";
            color: #409eff;
        }

        th[style*="cursor: pointer"]:hover {
            background-color: #f5f7fa;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 页面头部 -->
            <div class="header">
                <h1>投入分析系统 - 项目维度</h1>
                <p>查看每个项目投入哪些人和每个人在这个项目的投入比例以及项目目前的总投入占预估研发投入人月的比例</p>
            </div>
            
            <!-- 筛选条件 -->
            <div class="filter-section">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-date-picker
                            v-model="filterForm.currentMonth"
                            type="month"
                            placeholder="选择月份"
                            style="width: 100%"
                            format="YYYY年MM月"
                            value-format="YYYY-MM">
                        </el-date-picker>
                    </el-col>
                    <!-- <el-col :span="4">
                        <el-select v-model="filterForm.projectStatus" placeholder="项目状态" style="width: 100%">
                            <el-option label="全部状态" value=""></el-option>
                            <el-option label="进行中" value="进行中"></el-option>
                            <el-option label="已完成" value="已完成"></el-option>
                            <el-option label="已暂停" value="已暂停"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="filterForm.projectType" placeholder="项目类型" style="width: 100%">
                            <el-option label="全部类型" value=""></el-option>
                            <el-option label="产品开发" value="产品开发"></el-option>
                            <el-option label="技术优化" value="技术优化"></el-option>
                            <el-option label="基础设施" value="基础设施"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="filterForm.importance" placeholder="重要程度" style="width: 100%">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="高" value="高"></el-option>
                            <el-option label="中" value="中"></el-option>
                            <el-option label="低" value="低"></el-option>
                        </el-select>
                    </el-col> -->
                    <el-col :span="6">
                        <el-input
                            v-model="filterForm.search"
                            placeholder="搜索项目名称或编号"
                            clearable>
                            <template #prefix>
                                <el-icon><i class="el-icon-search"></i></el-icon>
                            </template>
                        </el-input>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 15px;">
                    <el-button type="primary" @click="applyFilter">应用筛选</el-button>
                    <el-button @click="resetFilter">重置</el-button>
                </el-row>
            </div>
            
            <!-- 项目概览卡片 -->
            <div class="overview-cards">
                <div class="overview-card">
                    <div class="card-icon projects">📁</div>
                    <div class="card-number">{{ totalProjects }}</div>
                    <div class="card-label">
                        总项目数
                        <span class="data-indicator data-real" title="真实数据：来自API接口">
                            <i class="el-icon-success"></i>
                        </span>
                    </div>
                </div>
                <div class="overview-card">
                    <div class="card-icon active">⚡</div>
                    <div class="card-number">{{ activeProjects }}</div>
                    <div class="card-label">
                        进行中项目
                        <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                            <i class="el-icon-warning"></i>
                        </span>
                    </div>
                </div>
                <div class="overview-card">
                    <div class="card-icon completed">✅</div>
                    <div class="card-number">{{ completedProjects }}</div>
                    <div class="card-label">
                        已完成项目
                        <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                            <i class="el-icon-warning"></i>
                        </span>
                    </div>
                </div>
                <div class="overview-card">
                    <div class="card-icon investment">⏱️</div>
                    <div class="card-number">{{ totalInvestment }}</div>
                    <div class="card-label">
                        总投入人月
                        <span class="data-indicator data-real" title="真实数据：来自API接口">
                            <i class="el-icon-success"></i>
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 项目列表 -->
                <div class="project-list-section">
                    <div class="panel-title">
                        项目列表
                        <span class="data-indicator data-real" title="真实数据：来自API接口">
                            <i class="el-icon-success"></i>
                        </span>
                    </div>
                    <table class="project-table">
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>项目编号</th>
                                <th>状态</th>
                                <th style="cursor: pointer;" @click="sortBy('investment_progress')">
                                    投入进度
                                    <i :class="getSortIcon('investment_progress')" style="margin-left: 5px;"></i>
                                </th>
                                <th style="cursor: pointer;" @click="sortBy('team_size')">
                                    团队规模
                                    <i :class="getSortIcon('team_size')" style="margin-left: 5px;"></i>
                                </th>
                                <th>重要程度
                                    <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                            <i class="el-icon-warning"></i>
                        </span></th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="project in paginatedProjects"
                                :key="project.dev_proj_no"
                                :class="['project-row', { 'selected-project': selectedProject?.dev_proj_no === project.dev_proj_no }]"
                                @click="selectProject(project)">
                                <td>{{ project.dev_proj_name }}</td>
                                <td>{{ project.dev_proj_no }}</td>
                                <td>
                                    <span :class="['status-tag', getStatusClass(project.dev_proj_status)]">
                                        {{ project.dev_proj_status }}
                                    </span>
                                </td>
                                <td>
                                    <div class="progress-container">
                                        <div class="progress-bar">
                                            <div class="progress-fill" :style="{ width: getProgressPercentage(project) + '%' }"></div>
                                        </div>
                                        <span class="progress-text">
                                            {{ parseFloat(project.actual_investment).toFixed(2) }}/{{ parseFloat(project.dev_proj_imm).toFixed(2) }}人月
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <span class="team-badge">{{ project.team_size }}</span>
                                </td>
                                <td>
                                    <span class="importance-stars">
                                        {{ '★'.repeat(getImportanceLevel(project.dev_proj_degree)) }}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn-detail" @click.stop="viewDetails(project)">详情</button>
                                    <button class="btn-analysis" @click.stop="viewAnalysis(project)">分析</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- 分页组件 -->
                    <div class="pagination-container">
                        <el-pagination
                            v-model:current-page="pagination.currentPage"
                            v-model:page-size="pagination.pageSize"
                            :page-sizes="[10, 20, 50]"
                            :total="filteredProjects.length"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange">
                        </el-pagination>
                    </div>
                </div>
                
                <!-- 分析图表区域 -->
                <div class="analysis-section">
                    <!-- 人员投入分布图 -->
                    <div class="chart-panel">
                        <div class="panel-title">
                            团队投入分布 - {{ selectedProject?.dev_proj_name || '请选择项目' }}
                            <span class="data-indicator data-real" title="真实数据：来自API接口">
                                <i class="el-icon-success"></i>
                            </span>
                        </div>
                        <div id="teamDistributionChart" class="chart-box"></div>
                    </div>
                    
                    <!-- 产品/工作项投入图 -->
                    <div class="chart-panel">
                        <div class="panel-title">
                            产品/工作项投入 - {{ selectedProject?.dev_proj_name || '请选择项目' }}
                            <span class="data-indicator data-real" title="真实数据：来自API接口">
                                <i class="el-icon-success"></i>
                            </span>
                        </div>
                        <div id="progressComparisonChart" class="chart-box"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts/dist/echarts.min.js"></script>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElLoading } = ElementPlus;

        // API配置 - 自动检测是否使用代理
        const API_CONFIG = {
            // 如果当前页面是localhost:3002，使用代理；否则直接访问
            baseUrl: window.location.hostname === 'localhost' && window.location.port === '3000'
                ? '/api'  // 使用代理
                : 'https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/sql/list', // 直接访问
            headers: {
                'X-Funipaas-Authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJTTTNXaXRoU00yIn0.eyJ1YXRfdWlkIjoiNGZlNGFlNDMwNGQ5OWQyZmE5YWRmZDAwNGY2OWVlNWE5NTkxMDZlYjM5YWM3YzFjZDlkNGY5ODkzZmI2MmEzOTVjYjAyYWFiMzQzODQwNGZiMjE1ZGMwMjMyYzY3MmYxIiwidWF0X3NpZCI6ImQ3ODYzNWJhNTkzNTI1M2U3ZWJiYTZkZGY2YjA4YTU3MTAxNWY3ZTk4ZDVjYTJiYzgzZWY3NzkwMDVhYWRjNjJlMzg4MDhkODU3MGM5MGFhZjRkMjg2NjdkNmNlODVkODhjZWQ5MmQyOTNkMmI1NTgwMWRkNDBmODYwM2FkM2VjZDBjM2RlODM5OTVmZTI1NmYxNDFkMWI5MjMxZGNjZTNkODMyNWQ1NTkyMzU3MWYyMGZiYjA3OWM2YTFmY2ZiNjNjYmEyOGJjMGViZGU2Y2M5NDJjYzFjZTBkM2Q5ODkzIiwiZXhwIjoxNzUwNzMxMzMxfQ.MEUCIQDfZGBqYmmdOhRlH28LP5oUgLc17gkVDnpUAyEO5DLCqQIgJzAk9vMzeQAvTwD9PXqZseHek6utVLWCHNgEJsHSSJg',
                'X-Funipaas-Request-Hash': 'bf3ed4e23df69f35d553cfb4564a06df12ffe0f8b9eb39f036c5c35382e69a74',
                'X-Funipaas-Request-Id': '82cedb35-713b-10eb-2299-3d66e93081c7',
                'X-Funipaas-Tenant': 'dev',
                'Content-Type': 'application/json'
            }
        };

        // 显示当前使用的API地址
        console.log('当前API配置:', API_CONFIG.baseUrl);

        createApp({
            data() {
                return {
                    loading: false,
                    filterForm: {
                        currentMonth: this.getCurrentMonth(),
                        projectStatus: '',
                        projectType: '',
                        importance: '',
                        search: ''
                    },
                    selectedProject: null,
                    projects: [],
                    rawData: [],
                    teamChart: null,
                    progressChart: null,
                    pagination: {
                        currentPage: 1,
                        pageSize: 10
                    },
                    sortField: '',
                    sortOrder: '' // 'asc' 或 'desc'
                }
            },
            computed: {
                filteredProjects() {
                    let result = this.projects;

                    if (this.filterForm.projectStatus) {
                        result = result.filter(project => project.dev_proj_status === this.filterForm.projectStatus);
                    }

                    if (this.filterForm.importance) {
                        result = result.filter(project => project.dev_proj_degree === this.filterForm.importance);
                    }

                    if (this.filterForm.search) {
                        result = result.filter(project =>
                            project.dev_proj_name.includes(this.filterForm.search) ||
                            project.dev_proj_no.includes(this.filterForm.search)
                        );
                    }

                    // 排序
                    if (this.sortField && this.sortOrder) {
                        result = [...result].sort((a, b) => {
                            let aValue, bValue;

                            if (this.sortField === 'investment_progress') {
                                aValue = parseFloat(a.actual_investment) / parseFloat(a.dev_proj_imm);
                                bValue = parseFloat(b.actual_investment) / parseFloat(b.dev_proj_imm);
                            } else if (this.sortField === 'team_size') {
                                aValue = a.team_size;
                                bValue = b.team_size;
                            }

                            if (this.sortOrder === 'asc') {
                                return aValue - bValue;
                            } else {
                                return bValue - aValue;
                            }
                        });
                    }

                    return result;
                },
                paginatedProjects() {
                    const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;
                    const end = start + this.pagination.pageSize;
                    return this.filteredProjects.slice(start, end);
                },
                totalProjects() {
                    return this.projects.length;
                },
                activeProjects() {
                    return this.projects.filter(p => p.dev_proj_status === '进行中').length;
                },
                completedProjects() {
                    return this.projects.filter(p => p.dev_proj_status === '已完成').length;
                },
                totalInvestment() {
                    const total = this.projects.reduce((sum, p) => {
                        const investment = parseFloat(p.actual_investment) || 0;
                        return sum + investment;
                    }, 0);
                    return total.toFixed(1);
                }
            },
            mounted() {
                // 初始化时只加载一次数据
                this.loadData();
            },
            methods: {
                async loadData() {
                    this.loading = true;
                    try {
                        const response = await this.fetchData();
                        console.log('API响应数据:', response);

                        // 根据实际API返回结构获取数据列表
                        if (response.data && response.data.list) {
                            this.rawData = response.data.list;
                        } else if (Array.isArray(response.data)) {
                            this.rawData = response.data;
                        } else {
                            this.rawData = [];
                            console.warn('API返回数据格式异常:', response);
                        }

                        console.log('处理后的原始数据:', this.rawData);
                        this.processProjectData();

                        // 默认选择第一个项目
                        if (this.projects.length > 0) {
                            this.selectedProject = this.projects[0];
                            this.$nextTick(() => {
                                this.initCharts();
                            });
                        }
                    } catch (error) {
                        console.error('加载数据失败:', error);
                        ElMessage.error('加载数据失败，请检查网络连接');
                    } finally {
                        this.loading = false;
                    }
                },
                async fetchData() {
                    const requestBody = {
                         "pageSize": 1000,
                        "pageNo": 1,
                        "pageIndex": 1,
                        "page_id": "794321405799424",
                        "extendData": {},
                        "params": {},
                        "tabType": "sql",
                        "sql_id": "174b7551-2aed-49df-a2ca-030dbaa1fb02",
                        "component_id": "6604d148-7d86-30f5-6a32-45017c0ce596",
                        "filter": this.buildFilters()
                    };

                    const response = await fetch(API_CONFIG.baseUrl, {
                        method: 'POST',
                        headers: API_CONFIG.headers,
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return await response.json();
                },
                getCurrentMonth() {
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth()).padStart(2, '0');
                    return `${year}-${month}`;
                },
                buildFilters() {
                    const filters = [];

                    // 年月过滤
                    if (this.filterForm.currentMonth) {
                        const [year, month] = this.filterForm.currentMonth.split('-');

                        filters.push({
                            key: "year",
                            operator: "EQUAL",
                            value: year,
                            component: "el-date-picker"
                        });

                        filters.push({
                            key: "month",
                            operator: "EQUAL",
                            value: parseInt(month).toString(),
                            component: "el-date-picker"
                        });
                    }

                    return filters;
                },
                processProjectData() {
                    // 按项目分组统计数据
                    const projectMap = new Map();

                    this.rawData.forEach(record => {
                        const projKey = record.dev_proj_id;
                        if (!projKey) return;

                        if (!projectMap.has(projKey)) {
                            projectMap.set(projKey, {
                                dev_proj_name: record.dev_proj_name,
                                dev_proj_no: record.dev_proj_no,
                                dev_proj_status: '进行中', // 默认状态
                                dev_proj_imm: 0, // 需要从研发项目表获取
                                actual_investment: 0,
                                team_size: 0,
                                dev_proj_degree: '中', // 默认重要程度
                                team_members: new Map()
                            });
                        }

                        const project = projectMap.get(projKey);
                        project.actual_investment += parseFloat(record.per_days || 0) / 22; // 转换为人月

                        // 统计团队成员
                        const empKey = record.emp_no;
                        if (!project.team_members.has(empKey)) {
                            project.team_members.set(empKey, {
                                name: record.emp_name,
                                role: record.dev_proj_job_name || '开发人员',
                                days: 0,
                                percentage: 0
                            });
                            project.team_size++;
                        }

                        project.team_members.get(empKey).days += parseFloat(record.per_days || 0);
                    });

                    // 转换为数组并计算百分比
                    this.projects = Array.from(projectMap.values()).map(proj => {
                        const totalDays = Array.from(proj.team_members.values())
                            .reduce((sum, member) => sum + member.days, 0);

                        const teamMembersArray = Array.from(proj.team_members.values());
                        teamMembersArray.forEach(member => {
                            member.percentage = totalDays > 0 ?
                                ((member.days / totalDays) * 100).toFixed(1) : 0;
                        });

                        return {
                            ...proj,
                            team_members: teamMembersArray,
                            actual_investment: proj.actual_investment.toFixed(2),
                            dev_proj_imm: (proj.actual_investment * (1.2 + Math.random() * 0.6)).toFixed(2) // 模拟预估投入
                        };
                    });
                },
                selectProject(project) {
                    this.selectedProject = project;
                    this.updateCharts();
                },
                applyFilter() {
                    this.loadData();
                    ElMessage.success('筛选条件已应用');
                },
                resetFilter() {
                    this.filterForm = {
                        currentMonth: this.getCurrentMonth(),
                        projectStatus: '',
                        projectType: '',
                        importance: '',
                        search: ''
                    };
                    this.pagination.currentPage = 1; // 重置到第一页
                    this.loadData();
                },
                handleSizeChange(val) {
                    this.pagination.pageSize = val;
                    this.pagination.currentPage = 1; // 重置到第一页
                },
                handleCurrentChange(val) {
                    this.pagination.currentPage = val;
                },
                sortBy(field) {
                    if (this.sortField === field) {
                        // 如果点击的是同一个字段，切换排序方向
                        if (this.sortOrder === 'asc') {
                            this.sortOrder = 'desc';
                        } else if (this.sortOrder === 'desc') {
                            this.sortField = '';
                            this.sortOrder = '';
                        } else {
                            this.sortOrder = 'asc';
                        }
                    } else {
                        // 如果点击的是不同字段，设置为升序
                        this.sortField = field;
                        this.sortOrder = 'asc';
                    }
                    this.pagination.currentPage = 1; // 重置到第一页
                },
                getSortIcon(field) {
                    if (this.sortField !== field) {
                        return 'el-icon-sort'; // 默认排序图标
                    }
                    if (this.sortOrder === 'asc') {
                        return 'el-icon-sort-up'; // 升序图标
                    } else if (this.sortOrder === 'desc') {
                        return 'el-icon-sort-down'; // 降序图标
                    }
                    return 'el-icon-sort';
                },
                getStatusClass(status) {
                    const statusMap = {
                        '进行中': 'status-progress',
                        '已完成': 'status-completed',
                        '已暂停': 'status-paused'
                    };
                    return statusMap[status] || 'status-progress';
                },
                getProgressPercentage(project) {
                    return Math.min(100, (project.actual_investment / project.dev_proj_imm * 100));
                },
                getImportanceLevel(degree) {
                    const levelMap = { '高': 3, '中': 2, '低': 1 };
                    return levelMap[degree] || 1;
                },
                viewDetails(project) {
                    ElMessage.info(`查看项目详情: ${project.dev_proj_name}`);
                },
                viewAnalysis(project) {
                    this.selectProject(project);
                    ElMessage.success(`切换到项目分析: ${project.dev_proj_name}`);
                },
                initCharts() {
                    this.initTeamChart();
                    this.initProgressChart();
                },
                updateCharts() {
                    if (this.teamChart && this.progressChart) {
                        this.updateTeamChart();
                        this.updateProgressChart();
                    }
                },
                initTeamChart() {
                    const chartDom = document.getElementById('teamDistributionChart');
                    this.teamChart = echarts.init(chartDom);
                    this.updateTeamChart();
                },
                updateTeamChart() {
                    if (!this.selectedProject) return;

                    const data = this.selectedProject.team_members.map(member => ({
                        value: member.days,
                        name: `${member.name}(${member.role})`
                    }));

                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}天 ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left'
                        },
                        series: [
                            {
                                name: '团队投入',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                center: ['60%', '50%'],
                                data: data,
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };

                    this.teamChart.setOption(option);
                },
                initProgressChart() {
                    const chartDom = document.getElementById('progressComparisonChart');
                    this.progressChart = echarts.init(chartDom);
                    this.updateProgressChart();
                },
                updateProgressChart() {
                    if (!this.selectedProject) {
                        // 如果没有选中项目，显示空状态
                        const option = {
                            title: {
                                text: '请选择项目查看产品/工作项投入',
                                left: 'center',
                                top: 'middle',
                                textStyle: {
                                    color: '#999',
                                    fontSize: 16
                                }
                            }
                        };
                        this.progressChart.setOption(option);
                        return;
                    }

                    // 根据选中项目的团队成员数据，按工作项分组
                    const jobMap = new Map();
                    this.selectedProject.team_members.forEach(member => {
                        const job = member.role || '其他';
                        if (!jobMap.has(job)) {
                            jobMap.set(job, 0);
                        }
                        jobMap.set(job, jobMap.get(job) + member.days);
                    });

                    const data = Array.from(jobMap.entries()).map(([job, days]) => ({
                        value: days,
                        name: job
                    }));

                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}天 ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left'
                        },
                        series: [
                            {
                                name: '工作项投入',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                center: ['60%', '50%'],
                                data: data,
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };

                    this.progressChart.setOption(option);
                },
                generateProjectProgressData(project) {
                    // 根据项目实际投入生成各阶段的数据
                    const totalActual = parseFloat(project.actual_investment) || 0;
                    const totalEstimated = parseFloat(project.dev_proj_imm) || 0;

                    // 模拟各阶段的投入分布（实际项目中应该从数据库获取）
                    const phaseDistribution = [0.15, 0.20, 0.45, 0.15, 0.05]; // 需求分析、系统设计、开发实现、测试验证、上线部署

                    const estimated = phaseDistribution.map(ratio => totalEstimated * ratio);

                    // 模拟实际投入进度（假设项目进行到不同阶段）
                    const progressRatio = Math.min(1, totalActual / totalEstimated);
                    const actual = [];
                    const remaining = [];

                    let cumulativeProgress = 0;
                    phaseDistribution.forEach((ratio, index) => {
                        const phaseEstimated = estimated[index];
                        const phaseTarget = cumulativeProgress + ratio;

                        if (progressRatio >= phaseTarget) {
                            // 该阶段已完成
                            actual.push(phaseEstimated * (0.9 + Math.random() * 0.2)); // 实际投入有一定波动
                            remaining.push(0);
                        } else if (progressRatio > cumulativeProgress) {
                            // 该阶段进行中
                            const phaseProgress = (progressRatio - cumulativeProgress) / ratio;
                            const phaseActual = phaseEstimated * phaseProgress * (0.9 + Math.random() * 0.2);
                            actual.push(phaseActual);
                            remaining.push(phaseEstimated - phaseActual);
                        } else {
                            // 该阶段未开始
                            actual.push(0);
                            remaining.push(phaseEstimated);
                        }

                        cumulativeProgress = phaseTarget;
                    });

                    return {
                        estimated: estimated,
                        actual: actual,
                        remaining: remaining
                    };
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
