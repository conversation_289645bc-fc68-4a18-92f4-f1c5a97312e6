@echo off
chcp 65001 >nul
title 投入分析系统服务器

echo 🚀 启动投入分析系统...
echo ================================

REM 切换到脚本所在目录
cd /d "%~dp0"

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 找到Python，启动HTTP服务器...
    
    REM 启动Python HTTP服务器
    echo 📡 启动服务器在端口8080...
    start /b python -m http.server 8080
    
    REM 等待服务器启动
    echo ⏳ 等待服务器启动...
    timeout /t 3 /nobreak >nul
    
    REM 显示访问地址
    echo.
    echo 🌐 服务器已启动，访问地址：
    echo    人员分析: http://localhost:8080/person-analysis.html
    echo    项目分析: http://localhost:8080/project-analysis.html
    echo    部门分析: http://localhost:8080/department-analysis.html
    echo    API测试:  http://localhost:8080/api-test.html
    echo.
    
    REM 自动打开浏览器
    echo 🔗 自动打开浏览器...
    start http://localhost:8080/person-analysis.html
    
    echo.
    echo 🛑 按任意键停止服务器
    echo ================================
    pause >nul
    
    REM 停止Python服务器
    echo.
    echo 🛑 正在停止服务器...
    taskkill /f /im python.exe >nul 2>&1
    echo ✅ 服务器已停止
    
) else (
    REM 尝试Python3
    python3 --version >nul 2>&1
    if %errorlevel% == 0 (
        echo ✅ 找到Python3，启动HTTP服务器...
        
        REM 启动Python3 HTTP服务器
        echo 📡 启动服务器在端口8080...
        start /b python3 -m http.server 8080
        
        REM 等待服务器启动
        echo ⏳ 等待服务器启动...
        timeout /t 3 /nobreak >nul
        
        REM 显示访问地址
        echo.
        echo 🌐 服务器已启动，访问地址：
        echo    人员分析: http://localhost:8080/person-analysis.html
        echo    项目分析: http://localhost:8080/project-analysis.html
        echo    部门分析: http://localhost:8080/department-analysis.html
        echo    API测试:  http://localhost:8080/api-test.html
        echo.
        
        REM 自动打开浏览器
        echo 🔗 自动打开浏览器...
        start http://localhost:8080/person-analysis.html
        
        echo.
        echo 🛑 按任意键停止服务器
        echo ================================
        pause >nul
        
        REM 停止Python服务器
        echo.
        echo 🛑 正在停止服务器...
        taskkill /f /im python3.exe >nul 2>&1
        taskkill /f /im python.exe >nul 2>&1
        echo ✅ 服务器已停止
        
    ) else (
        echo ❌ 错误: 未找到Python
        echo.
        echo 请安装Python后重试，或使用其他方法：
        echo 1. 安装Python: https://www.python.org/downloads/
        echo 2. 使用Node.js: npm install -g http-server ^&^& http-server -p 8080
        echo 3. 使用VS Code Live Server插件
        echo.
        pause
    )
)
