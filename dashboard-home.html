<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发中心管理仪表盘</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .dashboard-container {
            min-height: 100vh;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .header h1 .data-indicator {
            font-size: 16px;
            font-weight: 400;
            padding: 4px 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.2);
        }

        .header-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-buttons {
            display: flex;
            gap: 8px;
        }

        .nav-btn {
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .nav-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-department {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-department:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .btn-project {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-project:hover {
            background: linear-gradient(135deg, #ee82e9 0%, #f3455a 100%);
        }

        .btn-person {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-person:hover {
            background: linear-gradient(135deg, #3d9aec 0%, #00e0ec 100%);
        }

        /* 全宽卡片样式 */
        .dashboard-card.full-width {
            grid-column: 1 / -1;
        }

        .header-stats {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }

        .header-stat {
            text-align: center;
        }

        .header-stat .number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }

        .header-stat .label {
            font-size: 14px;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            border: 1px solid #ebeef5;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #ebeef5;
        }

        .card-indicators {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-icon {
            font-size: 20px;
        }

        .chart-container {
            height: 300px;
            width: 100%;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .metric-item {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 4px;
        }

        .metric-label {
            font-size: 14px;
            color: #606266;
        }

        .trend-indicator {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 8px;
        }

        .trend-up {
            background: #f0f9ff;
            color: #67c23a;
        }

        .trend-down {
            background: #fef0f0;
            color: #f56c6c;
        }

        .alert-list {
            max-height: 280px;
            overflow-y: auto;
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 8px;
            border-left: 4px solid;
        }

        .alert-high {
            background: #fef0f0;
            border-left-color: #f56c6c;
        }

        .alert-medium {
            background: #fdf6ec;
            border-left-color: #e6a23c;
        }

        .alert-low {
            background: #f0f9ff;
            border-left-color: #409eff;
        }

        .alert-icon {
            font-size: 16px;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .alert-desc {
            font-size: 12px;
            color: #606266;
        }

        .quick-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .action-btn {
            flex: 1;
            min-width: 120px;
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }

        .btn-primary {
            background: #409eff;
            color: white;
        }

        .btn-success {
            background: #67c23a;
            color: white;
        }

        .btn-warning {
            background: #e6a23c;
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .data-indicator {
            display: inline-flex;
            align-items: center;
            margin-left: 8px;
            font-size: 14px;
        }

        .data-real {
            color: #67c23a;
        }

        .data-mock {
            color: #e6a23c;
        }

        .indicator-tooltip {
            margin-left: 4px;
            cursor: help;
        }

        /* 数据标识图标样式 */
        .data-indicator .el-icon-success::before {
            content: "✓";
            font-style: normal;
            font-weight: bold;
        }

        .data-indicator .el-icon-warning::before {
            content: "⚠️";
            font-style: normal;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #909399;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .dashboard-container {
                padding: 10px;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .header-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .header-actions {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
                width: 100%;
            }

            .nav-buttons {
                justify-content: space-between;
                width: 100%;
            }

            .nav-btn {
                flex: 1;
                text-align: center;
                padding: 10px 8px;
                font-size: 12px;
            }

            .metric-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .metric-item {
                padding: 15px;
            }

            .metric-value {
                font-size: 20px;
            }

            .alert-list {
                max-height: 200px;
                overflow-y: auto;
            }

            .chart-container {
                height: 250px;
            }

            .card-title {
                font-size: 14px;
            }

            .card-icon {
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .header-stat .number {
                font-size: 20px;
            }

            .header-stat .label {
                font-size: 12px;
            }

            .nav-btn {
                padding: 8px 6px;
                font-size: 11px;
            }

            .metric-grid {
                grid-template-columns: 1fr;
            }

            .chart-container {
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="dashboard-container">
            <!-- 页面头部 -->
            <div class="header">
                <h1>🎯 研发中心管理仪表盘
                    <span class="data-indicator data-real" title="真实数据：来自API接口">
                         <i class="el-icon-success">真实api数据</i>
                    </span>
                    <!-- <span class="data-indicator data-real" title="真实数据：来自API接口">
                        √ 真实api数据
                    </span> -->
                    <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                        ⚠️ 模拟数据
                    </span>
                </h1>
                <div class="header-info">
                    <div class="header-stats">
                        <div class="header-stat">
                            <span class="number">{{ totalProjects }}</span>
                            <span class="label">
                                在研项目
                                <span class="data-indicator data-real" title="真实数据：来自API接口">
                                    <i class="el-icon-success"></i>
                                </span>
                            </span>
                        </div>
                        <div class="header-stat">
                            <span class="number">{{ totalEmployees }}</span>
                            <span class="label">
                                研发人员
                                <span class="data-indicator data-real" title="真实数据：来自API接口">
                                    <i class="el-icon-success"></i>
                                </span>
                            </span>
                        </div>
                        <div class="header-stat">
                            <span class="number">{{ totalInvestment }}</span>
                            <span class="label">
                                月度投入(人月)
                                <span class="data-indicator data-real" title="真实数据：来自API接口">
                                    <i class="el-icon-success"></i>
                                </span>
                            </span>
                        </div>
                        <div class="header-stat">
                            <span class="number">{{ avgEfficiency }}%</span>
                            <span class="label">
                                平均效率
                                <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                                    <i class="el-icon-warning"></i>
                                </span>
                            </span>
                        </div>
                    </div>
                    <div class="header-actions">
                        <div class="nav-buttons">
                            <a href="department-analysis.html" target="_blank" class="nav-btn btn-department">部门</a>
                            <a href="project-analysis.html" target="_blank" class="nav-btn btn-project">项目</a>
                            <a href="person-analysis.html" target="_blank" class="nav-btn btn-person">人员</a>
                        </div>
                        <el-date-picker
                            v-model="currentMonth"
                            type="month"
                            placeholder="选择月份"
                            format="YYYY年MM月"
                            value-format="YYYY-MM"
                            @change="loadDashboardData"
                            style="width: 160px;">
                        </el-date-picker>
                    </div>
                </div>
            </div>

            <!-- 第一行：区域工时投入 - 占满整行 -->
            <div class="dashboard-grid">
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <div class="card-title">
                            <span class="card-icon">🌍</span>
                            区域工时投入
                            <span class="data-indicator data-real" title="真实数据：来自API接口">
                                <i class="el-icon-success"></i>
                            </span>
                        </div>
                    </div>
                    <div class="card-content">
                        <div id="regionWorkHoursChart" class="chart-container"></div>
                    </div>
                </div>
            </div>

            <!-- 第二行：资源配置概览和其他图表 -->
            <div class="dashboard-grid">
                <!-- 资源配置概览 -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">
                            <span class="card-icon">👥</span>
                            资源配置概览
                            <span class="data-indicator data-real" title="真实数据：来自API接口">
                                <i class="el-icon-success"></i>
                            </span>
                        </div>
                    </div>
                    <div class="chart-container" id="resourceChart"></div>
                </div>

                <!-- 项目健康度分析 -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">
                            <span class="card-icon">💊</span>
                            项目健康度分析
                            <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                                <i class="el-icon-warning"></i>
                            </span>
                        </div>
                    </div>
                    <div class="card-content">
                        <div id="projectHealthChart2" class="chart-container"></div>
                    </div>
                </div>

                <!-- 投入趋势 -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">
                            <span class="card-icon">📈</span>
                            投入趋势分析
                            <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                                <i class="el-icon-warning"></i>
                            </span>
                        </div>
                    </div>
                    <div class="chart-container" id="trendChart"></div>
                </div>

                <!-- 关键指标 -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">
                            <span class="card-icon">🎯</span>
                            关键绩效指标
                            <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                                <i class="el-icon-warning"></i>
                            </span>
                        </div>
                    </div>
                    <div class="metric-grid">
                        <div class="metric-item">
                            <div class="metric-value">{{ onTimeProjects }}%</div>
                            <div class="metric-label">按时交付率</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ resourceUtilization }}%</div>
                            <div class="metric-label">资源利用率</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ costVariance }}%</div>
                            <div class="metric-label">成本偏差</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ teamSatisfaction }}</div>
                            <div class="metric-label">团队满意度</div>
                        </div>
                    </div>
                </div>

                <!-- 风险预警 -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">
                            <span class="card-icon">⚠️</span>
                            风险预警中心
                            <span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                                <i class="el-icon-warning"></i>
                            </span>
                        </div>
                    </div>
                    <div class="alert-list">
                        <div v-for="alert in alerts" :key="alert.id" 
                             :class="['alert-item', `alert-${alert.level}`]">
                            <span class="alert-icon">{{ alert.icon }}</span>
                            <div class="alert-content">
                                <div class="alert-title">{{ alert.title }}</div>
                                <div class="alert-desc">{{ alert.description }}</div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts/dist/echarts.min.js"></script>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElLoading } = ElementPlus;

        // API配置
        const API_CONFIG = {
            baseUrl: window.location.hostname === 'localhost' && window.location.port === '3000'
                ? '/api'
                : 'https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/sql/list',
            headers: {
                'X-Funipaas-Authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJTTTNXaXRoU00yIn0.eyJ1YXRfdWlkIjoiNGZlNGFlNDMwNGQ5OWQyZmE5YWRmZDAwNGY2OWVlNWE5NTkxMDZlYjM5YWM3YzFjZDlkNGY5ODkzZmI2MmEzOTVjYjAyYWFiMzQzODQwNGZiMjE1ZGMwMjMyYzY3MmYxIiwidWF0X3NpZCI6ImQ3ODYzNWJhNTkzNTI1M2U3ZWJiYTZkZGY2YjA4YTU3MTAxNWY3ZTk4ZDVjYTJiYzgzZWY3NzkwMDVhYWRjNjJlMzg4MDhkODU3MGM5MGFhZjRkMjg2NjdkNmNlODVkODhjZWQ5MmQyOTNkMmI1NTgwMWRkNDBmODYwM2FkM2VjZDBjM2RlODM5OTVmZTI1NmYxNDFkMWI5MjMxZGNjZTNkODMyNWQ1NTkyMzU3MWYyMGZiYjA3OWM2YTFmY2ZiNjNjYmEyOGJjMGViZGU2Y2M5NDJjYzFjZTBkM2Q5ODkzIiwiZXhwIjoxNzUwNzMxMzMxfQ.MEUCIQDfZGBqYmmdOhRlH28LP5oUgLc17gkVDnpUAyEO5DLCqQIgJzAk9vMzeQAvTwD9PXqZseHek6utVLWCHNgEJsHSSJg',
                'X-Funipaas-Request-Hash': 'bf3ed4e23df69f35d553cfb4564a06df12ffe0f8b9eb39f036c5c35382e69a74',
                'X-Funipaas-Request-Id': '82cedb35-713b-10eb-2299-3d66e93081c7',
                'X-Funipaas-Tenant': 'dev',
                'Content-Type': 'application/json'
            }
        };

        createApp({
            data() {
                return {
                    loading: false,
                    currentMonth: this.getCurrentMonth(),
                    rawData: [],

                    // API调用控制
                    apiCallCount: 0,
                    lastApiCall: null,
                    debounceTimer: null,

                    // 头部统计数据
                    totalProjects: 0,
                    totalEmployees: 0,
                    totalInvestment: '0.0',
                    avgEfficiency: 0,
                    
                    // 关键指标
                    onTimeProjects: 85,
                    resourceUtilization: 78,
                    costVariance: -5,
                    teamSatisfaction: 4.2,
                    
                    // 预警信息
                    alerts: [
                        {
                            id: 1,
                            level: 'high',
                            icon: '🔴',
                            title: '数飞平台三期进度延期',
                            description: '预计延期2周，需要增加人力投入'
                        },
                        {
                            id: 2,
                            level: 'medium',
                            icon: '🟡',
                            title: '前端开发部人员负载过高',
                            description: '平均工作负载达到95%，建议调整任务分配'
                        },
                        {
                            id: 3,
                            level: 'low',
                            icon: '🔵',
                            title: '移动端优化项目资源充足',
                            description: '当前进度良好，可考虑提前交付'
                        }
                    ],
                    
                    // 图表实例
                    resourceChart: null,
                    projectHealthChart: null,
                    trendChart: null,
                    regionWorkHoursChart: null
                }
            },
            mounted() {
                console.log('页面加载，开始初始化数据');
                this.loadDashboardData();
            },

            beforeUnmount() {
                // 清理定时器
                if (this.debounceTimer) {
                    clearTimeout(this.debounceTimer);
                }
            },
            methods: {
                getCurrentMonth() {
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth()).padStart(2, '0');
                    return `${year}-${month}`;
                },
                
                async loadDashboardData() {
                    // 防抖处理：如果在500ms内重复调用，则取消之前的调用
                    if (this.debounceTimer) {
                        clearTimeout(this.debounceTimer);
                    }

                    this.debounceTimer = setTimeout(async () => {
                        await this.doLoadDashboardData();
                    }, 300);
                },

                async doLoadDashboardData() {
                    // 防止重复调用
                    const now = Date.now();
                    if (this.lastApiCall && (now - this.lastApiCall) < 1000) {
                        console.log('API调用过于频繁，跳过此次调用');
                        return;
                    }

                    this.apiCallCount++;
                    this.lastApiCall = now;
                    console.log(`API调用次数: ${this.apiCallCount}, 时间: ${new Date().toLocaleTimeString()}`);

                    if (this.loading) {
                        console.log('正在加载中，跳过重复调用');
                        return;
                    }

                    this.loading = true;
                    try {
                        const response = await this.fetchData();
                        console.log('仪表盘数据:', response);

                        if (response.data && response.data.list) {
                            this.rawData = response.data.list;
                        } else if (Array.isArray(response.data)) {
                            this.rawData = response.data;
                        } else {
                            this.rawData = [];
                        }

                        this.processData();
                        this.$nextTick(() => {
                            this.initCharts();
                        });

                    } catch (error) {
                        console.error('加载仪表盘数据失败:', error);
                        ElMessage.error('加载数据失败，请稍后重试');
                    } finally {
                        this.loading = false;
                    }
                },
                
                async fetchData() {
                    const requestBody = {
                        "pageSize": 1000,
                        "pageNo": 1,
                        "pageIndex": 1,
                        "page_id": "794321405799424",
                        "extendData": {},
                        "params": {},
                        "tabType": "sql",
                        "sql_id": "174b7551-2aed-49df-a2ca-030dbaa1fb02",
                        "component_id": "6604d148-7d86-30f5-6a32-45017c0ce596",
                        "filter": this.buildFilters()
                    };

                    const response = await fetch(API_CONFIG.baseUrl, {
                        method: 'POST',
                        headers: API_CONFIG.headers,
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return await response.json();
                },
                
                buildFilters() {
                    const filters = [];
                    
                    if (this.currentMonth) {
                        const [year, month] = this.currentMonth.split('-');
                        
                        filters.push({
                            key: "year",
                            operator: "EQUAL",
                            value: year,
                            component: "el-date-picker"
                        });
                        
                        filters.push({
                            key: "month",
                            operator: "EQUAL",
                            value: parseInt(month).toString(),
                            component: "el-date-picker"
                        });
                    }
                    
                    return filters;
                },
                
                processData() {
                    // 统计项目数量
                    const projects = new Set();
                    const employees = new Set();
                    let totalDays = 0;
                    
                    this.rawData.forEach(record => {
                        if (record.dev_proj_id) {
                            projects.add(record.dev_proj_id);
                        }
                        if (record.emp_no) {
                            employees.add(record.emp_no);
                        }
                        totalDays += parseFloat(record.per_days || 0);
                    });
                    
                    this.totalProjects = projects.size;
                    this.totalEmployees = employees.size;
                    this.totalInvestment = (totalDays / 22).toFixed(1); // 转换为人月
                    this.avgEfficiency = Math.round(75 + Math.random() * 20); // 模拟效率数据
                },
                
                initCharts() {
                    this.initResourceChart();
                    this.initProjectHealthChart();
                    this.initRegionWorkHoursChart();
                    this.initTrendChart();
                },

                initResourceChart() {
                    const chartDom = document.getElementById('resourceChart');
                    this.resourceChart = echarts.init(chartDom);

                    // 统计部门人员分布
                    const deptMap = new Map();
                    this.rawData.forEach(record => {
                        const dept = record.first_dept_name || '未分类';
                        if (!deptMap.has(dept)) {
                            deptMap.set(dept, new Set());
                        }
                        deptMap.get(dept).add(record.emp_no);
                    });

                    const data = Array.from(deptMap.entries()).map(([dept, employees]) => ({
                        name: dept,
                        value: employees.size
                    }));

                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}人 ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left',
                            textStyle: {
                                fontSize: 12
                            }
                        },
                        series: [
                            {
                                name: '人员分布',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                center: ['60%', '50%'],
                                data: data,
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                },
                                label: {
                                    fontSize: 11
                                }
                            }
                        ]
                    };

                    this.resourceChart.setOption(option);
                },

                initProjectHealthChart() {
                    // 初始化第二行的项目健康度分析图表
                    const chartDom = document.getElementById('projectHealthChart2');
                    if (!chartDom) return;
                    this.projectHealthChart = echarts.init(chartDom);

                    // 模拟项目健康度数据
                    const healthData = [
                        { name: '健康', value: Math.floor(this.totalProjects * 0.6), itemStyle: { color: '#67C23A' } },
                        { name: '风险', value: Math.floor(this.totalProjects * 0.3), itemStyle: { color: '#E6A23C' } },
                        { name: '延期', value: Math.floor(this.totalProjects * 0.1), itemStyle: { color: '#F56C6C' } }
                    ];

                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}个项目 ({d}%)'
                        },
                        legend: {
                            bottom: '5%',
                            left: 'center'
                        },
                        series: [
                            {
                                name: '项目状态',
                                type: 'pie',
                                radius: ['30%', '60%'],
                                center: ['50%', '45%'],
                                data: healthData,
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };

                    this.projectHealthChart.setOption(option);
                },

                initRegionWorkHoursChart() {
                    const chartDom = document.getElementById('regionWorkHoursChart');
                    this.regionWorkHoursChart = echarts.init(chartDom);

                    // 根据API数据统计区域工时投入
                    const regionData = this.calculateRegionWorkHours();

                    const option = {
                        title: {
                            text: '区域工时投入分布',
                            left: 'center',
                            textStyle: {
                                fontSize: 16,
                                fontWeight: 'bold'
                            }
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            formatter: function(params) {
                                const data = params[0];
                                return `${data.name}<br/>投入：${data.value}人天`;
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: regionData.map(item => item.region),
                            axisLabel: {
                                rotate: 45,
                                interval: 0
                            }
                        },
                        yAxis: {
                            type: 'value',
                            name: '投入（人天）',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        },
                        series: [{
                            name: '投入',
                            type: 'bar',
                            data: regionData.map(item => item.workHours),
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: '#409EFF' },
                                    { offset: 1, color: '#79BBFF' }
                                ])
                            },
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '{c}'
                            }
                        }]
                    };

                    this.regionWorkHoursChart.setOption(option);
                },

                calculateRegionWorkHours() {
                    if (!this.rawData || this.rawData.length === 0) {
                        return [];
                    }

                    // 按区域（region_name字段）统计工时投入
                    const regionMap = new Map();

                    this.rawData.forEach(item => {
                        const region = item.region_name || '未分类';
                        const days = parseFloat(item.per_days || 0);

                        if (!regionMap.has(region)) {
                            regionMap.set(region, 0);
                        }

                        regionMap.set(region, regionMap.get(region) + days);
                    });

                    // 转换为数组并按投入降序排列
                    return Array.from(regionMap.entries())
                        .map(([region, workHours]) => ({
                            region,
                            workHours: parseFloat(workHours.toFixed(1))
                        }))
                        .sort((a, b) => b.workHours - a.workHours);
                },

                initTrendChart() {
                    const chartDom = document.getElementById('trendChart');
                    this.trendChart = echarts.init(chartDom);

                    // 生成最近6个月的趋势数据
                    const months = [];
                    const investmentData = [];
                    const efficiencyData = [];

                    for (let i = 5; i >= 0; i--) {
                        const date = new Date();
                        date.setMonth(date.getMonth() - i);
                        months.push(`${date.getMonth() + 1}月`);

                        // 模拟投入和效率数据
                        const baseInvestment = parseFloat(this.totalInvestment);
                        investmentData.push((baseInvestment * (0.8 + Math.random() * 0.4)).toFixed(1));
                        efficiencyData.push(Math.round(70 + Math.random() * 25));
                    }

                    const option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross'
                            }
                        },
                        legend: {
                            data: ['投入(人月)', '效率(%)']
                        },
                        xAxis: {
                            type: 'category',
                            data: months
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: '投入(人月)',
                                position: 'left'
                            },
                            {
                                type: 'value',
                                name: '效率(%)',
                                position: 'right'
                            }
                        ],
                        series: [
                            {
                                name: '投入(人月)',
                                type: 'bar',
                                data: investmentData,
                                itemStyle: {
                                    color: '#409EFF'
                                }
                            },
                            {
                                name: '效率(%)',
                                type: 'line',
                                yAxisIndex: 1,
                                data: efficiencyData,
                                itemStyle: {
                                    color: '#67C23A'
                                },
                                smooth: true
                            }
                        ]
                    };

                    this.trendChart.setOption(option);
                },

                exportReport() {
                    ElMessage.success('报告导出功能开发中...');
                },

                scheduleReview() {
                    ElMessage.success('评审安排功能开发中...');
                },

                resourceOptimize() {
                    ElMessage.success('资源优化建议功能开发中...');
                },

                // 重置API调用计数器（用于调试）
                resetApiCallCount() {
                    this.apiCallCount = 0;
                    this.lastApiCall = null;
                    console.log('API调用计数器已重置');
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
