<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投入分析系统 - 部门维度</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #303133;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .filter-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 30% 70%;
            gap: 20px;
            min-height: 700px;
        }
        
        .left-panel {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .right-panel {
            display: grid;
            grid-template-rows: auto auto 1fr;
            gap: 20px;
        }
        
        .overview-section {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }
        
        .overview-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .overview-card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }
        
        .card-icon {
            font-size: 24px;
            margin-right: 8px;
        }
        
        .card-icon.projects { color: #409EFF; }
        .card-icon.employees { color: #67C23A; }
        .card-icon.investment { color: #E6A23C; }
        
        .card-title {
            font-size: 14px;
            color: #606266;
        }
        
        .card-value {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
        }
        
        .card-trend {
            font-size: 12px;
            color: #909399;
        }
        
        .trend-up { color: #67C23A; }
        .trend-down { color: #F56C6C; }
        
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .chart-panel {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #409EFF;
        }
        
        .department-tree {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .tree-node {
            display: flex;
            align-items: center;
            padding: 5px 0;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .tree-node:hover {
            background-color: #f5f7fa;
        }
        
        .tree-node.selected {
            background-color: #ecf5ff;
            color: #409eff;
        }
        
        .node-icon {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .node-label {
            flex: 1;
            font-size: 14px;
        }
        
        .node-badge {
            background-color: #409eff;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 12px;
            margin-left: 8px;
        }
        
        .chart-box {
            height: 300px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
        }
        
        .ranking-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .ranking-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .ranking-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin-right: 15px;
        }
        
        .rank-1 { background-color: #FFD700; }
        .rank-2 { background-color: #C0C0C0; }
        .rank-3 { background-color: #CD7F32; }
        .ranking-number:not(.rank-1):not(.rank-2):not(.rank-3) { 
            background-color: #909399; 
        }
        
        .project-info {
            flex: 1;
            margin-right: 15px;
        }
        
        .project-name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 5px;
        }
        
        .project-progress {
            width: 100%;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #e4e7ed;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #409eff;
            transition: width 0.3s ease;
        }
        
        .investment-info {
            text-align: right;
        }
        
        .investment-value {
            font-weight: bold;
            color: #303133;
        }
        
        .investment-ratio {
            font-size: 12px;
            color: #909399;
        }
        
        .dept-tree-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .dept-tree-item:hover {
            background-color: #f5f7fa;
        }
        
        .dept-tree-item.selected {
            background-color: #ecf5ff;
            color: #409eff;
        }
        
        .dept-tree-item.level-0 {
            font-weight: bold;
            background-color: #fafafa;
        }
        
        .dept-tree-item.level-1 {
            margin-left: 20px;
        }
        
        .dept-tree-item.level-2 {
            margin-left: 40px;
        }
        
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .overview-section {
                grid-template-columns: 1fr;
            }

            .charts-section {
                grid-template-columns: 1fr;
            }
        }

        .data-indicator {
            display: inline-flex;
            align-items: center;
            margin-left: 8px;
            font-size: 14px;
        }

        .data-real {
            color: #67c23a;
        }

        .data-mock {
            color: #e6a23c;
        }

        .indicator-tooltip {
            margin-left: 4px;
            cursor: help;
        }

        /* 数据标识图标样式 */
        .data-indicator .el-icon-success::before {
            content: "✓";
            font-style: normal;
            font-weight: bold;
        }

        .data-indicator .el-icon-warning::before {
            content: "⚠️";
            font-style: normal;
        }

        /* 人员投入排行榜样式 */
        .employee-ranking-list {
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }

        .employee-ranking-header {
            display: grid;
            grid-template-columns: 80px 1fr 120px 100px;
            background: #f5f7fa;
            padding: 12px 16px;
            font-weight: 600;
            color: #606266;
            border-bottom: 1px solid #e4e7ed;
        }

        .employee-ranking-item {
            display: grid;
            grid-template-columns: 80px 1fr 120px 100px;
            padding: 12px 16px;
            border-bottom: 1px solid #f0f2f5;
            align-items: center;
            transition: background-color 0.3s;
        }

        .employee-ranking-item:hover {
            background-color: #f5f7fa;
        }

        .employee-ranking-item:last-child {
            border-bottom: none;
        }

        .employee-name {
            font-weight: 500;
            color: #303133;
        }

        .employee-days {
            color: #409eff;
            font-weight: 500;
        }

        .employee-ratio {
            color: #67c23a;
            font-weight: 600;
        }

        /* 项目排行榜选中状态 */
        .ranking-item.selected {
            background-color: #e6f7ff;
            border-left: 4px solid #409eff;
        }

        .ranking-item:hover {
            background-color: #f5f7fa;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 15px 0;
            }

            .header h1 {
                font-size: 22px;
                margin-bottom: 8px;
            }

            .header p {
                font-size: 13px;
            }

            .filters {
                flex-direction: column;
                gap: 10px;
                margin-bottom: 20px;
            }

            .filter-group {
                width: 100%;
            }

            .filter-group label {
                font-size: 13px;
                margin-bottom: 5px;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .overview-section {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .overview-card {
                padding: 15px;
            }

            .overview-card h3 {
                font-size: 14px;
            }

            .overview-card .number {
                font-size: 20px;
            }

            .analysis-section {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .chart-container {
                height: 250px;
            }

            .ranking-list {
                max-height: 300px;
            }

            .ranking-header,
            .ranking-item {
                grid-template-columns: 50px 1fr 70px;
                padding: 8px;
                font-size: 12px;
            }

            .employee-ranking-header,
            .employee-ranking-item {
                grid-template-columns: 50px 1fr 70px 70px;
                padding: 8px;
                font-size: 12px;
            }

            .pagination-container {
                justify-content: center;
                margin-top: 15px;
            }

            .el-pagination {
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 20px;
            }

            .chart-container {
                height: 200px;
            }

            .ranking-header,
            .ranking-item {
                grid-template-columns: 50px 1fr 70px;
                padding: 6px;
                font-size: 12px;
            }

            .employee-ranking-header,
            .employee-ranking-item {
                grid-template-columns: 50px 1fr 70px 70px;
                padding: 6px;
                font-size: 12px;
            }

            .el-pagination {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 页面头部 -->
            <div class="header">
                <h1>投入分析系统 - 部门维度</h1>
                <p>查看每个部门有哪些项目和每个项目的投入以及项目目前的总投入占预估研发投入人月的比例</p>
            </div>
            
            <!-- 筛选条件 -->
            <div class="filter-section">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-date-picker
                            v-model="filterForm.currentMonth"
                            type="month"
                            placeholder="选择月份"
                            style="width: 100%"
                            format="YYYY年MM月"
                            value-format="YYYY-MM">
                        </el-date-picker>
                    </el-col>
                    <!-- <el-col :span="6">
                        <el-select v-model="filterForm.deptLevel" placeholder="部门层级" style="width: 100%">
                            <el-option label="全部层级" value=""></el-option>
                            <el-option label="副总级部门" value="副总级"></el-option>
                            <el-option label="一级部门" value="一级"></el-option>
                            <el-option label="二级部门" value="二级"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <el-select v-model="filterForm.projectStatus" placeholder="项目状态" style="width: 100%">
                            <el-option label="全部状态" value=""></el-option>
                            <el-option label="进行中" value="进行中"></el-option>
                            <el-option label="已完成" value="已完成"></el-option>
                            <el-option label="已暂停" value="已暂停"></el-option>
                        </el-select>
                    </el-col> -->
                    <el-col :span="6">
                        <el-input
                            v-model="filterForm.search"
                            placeholder="搜索部门或项目"
                            clearable>
                            <template #prefix>
                                <el-icon><i class="el-icon-search"></i></el-icon>
                            </template>
                        </el-input>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 15px;">
                    <el-button type="primary" @click="applyFilter">应用筛选</el-button>
                    <el-button @click="resetFilter">重置</el-button>
                </el-row>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 左侧部门树 -->
                <div class="left-panel">
                    <div class="panel-title">
                        部门组织架构
                        <span class="data-indicator data-real" title="真实数据：来自API接口">
                            <i class="el-icon-success"></i>
                        </span>
                    </div>
                    <div class="department-tree">
                        <template v-for="dept in departmentTree" :key="dept.id">
                            <div class="dept-tree-item level-0"
                                 :class="{ selected: selectedDept?.id === dept.id }"
                                 @click="selectDepartment(dept)">
                                <span class="node-icon">📁</span>
                                <span class="node-label">{{ dept.name }}</span>
                                <span class="node-badge" v-if="dept.project_count > 0">{{ dept.project_count }}</span>
                            </div>
                            <template v-for="child in dept.children" :key="child.id">
                                <div class="dept-tree-item level-1"
                                     :class="{ selected: selectedDept?.id === child.id }"
                                     @click="selectDepartment(child)">
                                    <span class="node-icon">📂</span>
                                    <span class="node-label">{{ child.name }}</span>
                                    <span class="node-badge" v-if="child.project_count > 0">{{ child.project_count }}</span>
                                </div>
                                <div v-for="grandchild in child.children" :key="grandchild.id" class="dept-tree-item level-2"
                                     :class="{ selected: selectedDept?.id === grandchild.id }"
                                     @click="selectDepartment(grandchild)">
                                    <span class="node-icon">📄</span>
                                    <span class="node-label">{{ grandchild.name }}</span>
                                    <span class="node-badge" v-if="grandchild.project_count > 0">{{ grandchild.project_count }}</span>
                                </div>
                            </template>
                        </template>
                    </div>
                </div>
                
                <!-- 右侧分析区域 -->
                <div class="right-panel">
                    <!-- 部门概览卡片 -->
                    <div class="overview-section">
                        <div class="overview-card">
                            <div class="card-header">
                                <span class="card-icon projects">📁</span>
                                <span class="card-title">
                                    项目数量
                                    <span class="data-indicator data-real" title="真实数据：来自API接口">
                                        <i class="el-icon-success"></i>
                                    </span>
                                </span>
                            </div>
                            <div class="card-value">{{ selectedDeptInfo.project_count }}</div>
                            <div class="card-trend">
                                <span class="trend-up">↗</span>
                                <span>较上月 +2<span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                                    <i class="el-icon-warning"></i>
                                </span></span>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-header">
                                <span class="card-icon employees">👥</span>
                                <span class="card-title">
                                    参与人员
                                    <span class="data-indicator data-real" title="真实数据：来自API接口">
                                        <i class="el-icon-success"></i>
                                    </span>
                                </span>
                            </div>
                            <div class="card-value">{{ selectedDeptInfo.employee_count }}</div>
                            <div class="card-trend">
                                <span class="trend-down">↘</span>
                                <span>较上月 -1<span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                                    <i class="el-icon-warning"></i>
                                </span></span>
                            </div>
                        </div>
                        <div class="overview-card">
                            <div class="card-header">
                                <span class="card-icon investment">⏱️</span>
                                <span class="card-title">
                                    总投入
                                    <span class="data-indicator data-real" title="真实数据：来自API接口">
                                        <i class="el-icon-success"></i>
                                    </span>
                                </span>
                            </div>
                            <div class="card-value">{{ selectedDeptInfo.total_investment }}人月</div>
                            <div class="card-trend">
                                <span class="trend-up">↗</span>
                                <span>较上月 +5.2<span class="data-indicator data-mock" title="模拟数据：基于算法生成">
                                    <i class="el-icon-warning"></i>
                                </span></span>
                            </div>
                        </div>
                    </div>
                    

                    
                    <!-- 项目投入排行榜 -->
                    <div class="chart-panel">
                        <div class="panel-title">
                            项目投入排行榜 - {{ selectedDept?.name || '请选择部门' }}
                            <span class="data-indicator data-real" title="真实数据：来自API接口">
                                <i class="el-icon-success"></i>
                            </span>
                        </div>
                        <div class="ranking-list">
                            <div v-for="(project, index) in paginatedProjects" :key="project.id"
                                 class="ranking-item"
                                 :class="{ 'selected': selectedProjectForEmployees?.id === project.id }"
                                 @click="selectProjectForEmployees(project)"
                                 style="cursor: pointer;">
                                <div class="ranking-number" :class="`rank-${(rankingPagination.currentPage - 1) * rankingPagination.pageSize + index + 1}`">
                                    {{ (rankingPagination.currentPage - 1) * rankingPagination.pageSize + index + 1 }}
                                </div>
                                <div class="project-info">
                                    <div class="project-name">{{ project.name }}</div>
                                    <div class="project-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill" :style="{ width: project.progress + '%' }"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="investment-info">
                                    <div class="investment-value">{{ project.investment }}人月</div>
                                    <div class="investment-ratio">{{ project.ratio }}%</div>
                                </div>
                            </div>
                        </div>

                        <!-- 排行榜分页 -->
                        <div class="pagination-container" style="margin-top: 20px;">
                            <el-pagination
                                v-model:current-page="rankingPagination.currentPage"
                                v-model:page-size="rankingPagination.pageSize"
                                :page-sizes="[5, 10, 20]"
                                :total="departmentProjects.length"
                                layout="total, sizes, prev, pager, next"
                                @size-change="handleRankingSizeChange"
                                @current-change="handleRankingCurrentChange"
                                small>
                            </el-pagination>
                        </div>
                    </div>

                    <!-- 人员投入排行榜 -->
                    <div v-if="selectedProjectForEmployees" class="chart-panel" style="margin-top: 20px;">
                        <div class="panel-title">
                            人员投入排行榜 - {{ selectedProjectForEmployees.name }}
                            <span class="data-indicator data-real" title="真实数据：来自API接口">
                                <i class="el-icon-success"></i>
                            </span>
                        </div>
                        <div class="employee-ranking-list">
                            <div class="employee-ranking-header">
                                <div class="header-item">排名</div>
                                <div class="header-item">员工姓名</div>
                                <div class="header-item">投入天数</div>
                                <div class="header-item">投入比例</div>
                            </div>
                            <div v-for="(employee, index) in paginatedEmployees" :key="employee.emp_no" class="employee-ranking-item">
                                <div class="ranking-number" :class="`rank-${(employeePagination.currentPage - 1) * employeePagination.pageSize + index + 1}`">
                                    {{ (employeePagination.currentPage - 1) * employeePagination.pageSize + index + 1 }}
                                </div>
                                <div class="employee-name">{{ employee.emp_name }}</div>
                                <div class="employee-days">{{ employee.days.toFixed(1) }}天</div>
                                <div class="employee-ratio">{{ employee.ratio }}%</div>
                            </div>
                        </div>

                        <!-- 人员排行榜分页 -->
                        <div class="pagination-container" style="margin-top: 20px;">
                            <el-pagination
                                v-model:current-page="employeePagination.currentPage"
                                v-model:page-size="employeePagination.pageSize"
                                :page-sizes="[5, 10, 20]"
                                :total="projectEmployees.length"
                                layout="total, sizes, prev, pager, next"
                                @size-change="handleEmployeeSizeChange"
                                @current-change="handleEmployeeCurrentChange"
                                small>
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts/dist/echarts.min.js"></script>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElLoading } = ElementPlus;

        // API配置 - 自动检测是否使用代理
        const API_CONFIG = {
            // 如果当前页面是localhost:3002，使用代理；否则直接访问
            baseUrl: window.location.hostname === 'localhost' && window.location.port === '3000'
                ? '/api'  // 使用代理
                : 'https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/sql/list', // 直接访问
            headers: {
                'X-Funipaas-Authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJTTTNXaXRoU00yIn0.eyJ1YXRfdWlkIjoiNGZlNGFlNDMwNGQ5OWQyZmE5YWRmZDAwNGY2OWVlNWE5NTkxMDZlYjM5YWM3YzFjZDlkNGY5ODkzZmI2MmEzOTVjYjAyYWFiMzQzODQwNGZiMjE1ZGMwMjMyYzY3MmYxIiwidWF0X3NpZCI6ImQ3ODYzNWJhNTkzNTI1M2U3ZWJiYTZkZGY2YjA4YTU3MTAxNWY3ZTk4ZDVjYTJiYzgzZWY3NzkwMDVhYWRjNjJlMzg4MDhkODU3MGM5MGFhZjRkMjg2NjdkNmNlODVkODhjZWQ5MmQyOTNkMmI1NTgwMWRkNDBmODYwM2FkM2VjZDBjM2RlODM5OTVmZTI1NmYxNDFkMWI5MjMxZGNjZTNkODMyNWQ1NTkyMzU3MWYyMGZiYjA3OWM2YTFmY2ZiNjNjYmEyOGJjMGViZGU2Y2M5NDJjYzFjZTBkM2Q5ODkzIiwiZXhwIjoxNzUwNzMxMzMxfQ.MEUCIQDfZGBqYmmdOhRlH28LP5oUgLc17gkVDnpUAyEO5DLCqQIgJzAk9vMzeQAvTwD9PXqZseHek6utVLWCHNgEJsHSSJg',
                'X-Funipaas-Request-Hash': 'bf3ed4e23df69f35d553cfb4564a06df12ffe0f8b9eb39f036c5c35382e69a74',
                'X-Funipaas-Request-Id': '82cedb35-713b-10eb-2299-3d66e93081c7',
                'X-Funipaas-Tenant': 'dev',
                'Content-Type': 'application/json'
            }
        };

        // 显示当前使用的API地址
        console.log('当前API配置:', API_CONFIG.baseUrl);

        createApp({
            data() {
                return {
                    loading: false,
                    filterForm: {
                        currentMonth: this.getCurrentMonth(),
                        deptLevel: '',
                        projectStatus: '',
                        search: ''
                    },
                    selectedDept: null,
                    rawData: [],
                    departmentTree: [],
                    departmentProjects: [],
                    rankingPagination: {
                        currentPage: 1,
                        pageSize: 5
                    },
                    selectedProjectForEmployees: null,
                    projectEmployees: [],
                    employeePagination: {
                        currentPage: 1,
                        pageSize: 5
                    }
                }
            },
            computed: {
                selectedDeptInfo() {
                    return this.selectedDept || {
                        project_count: 0,
                        employee_count: 0,
                        total_investment: 0
                    };
                },
                paginatedProjects() {
                    const start = (this.rankingPagination.currentPage - 1) * this.rankingPagination.pageSize;
                    const end = start + this.rankingPagination.pageSize;
                    return this.departmentProjects.slice(start, end);
                },
                paginatedEmployees() {
                    const start = (this.employeePagination.currentPage - 1) * this.employeePagination.pageSize;
                    const end = start + this.employeePagination.pageSize;
                    return this.projectEmployees.slice(start, end);
                }
            },
            mounted() {
                // 初始化时只加载一次数据
                this.loadData();
            },
            methods: {
                async loadData() {
                    this.loading = true;
                    try {
                        const response = await this.fetchData();
                        console.log('API响应数据:', response);

                        // 根据实际API返回结构获取数据列表
                        if (response.data && response.data.list) {
                            this.rawData = response.data.list;
                        } else if (Array.isArray(response.data)) {
                            this.rawData = response.data;
                        } else {
                            this.rawData = [];
                            console.warn('API返回数据格式异常:', response);
                        }

                        console.log('处理后的原始数据:', this.rawData);
                        this.processDepartmentData();

                        // 默认选择第一个部门
                        if (this.departmentTree.length > 0) {
                            this.selectedDept = this.departmentTree[0];
                            this.$nextTick(() => {
                                this.initCharts();
                            });
                        }
                    } catch (error) {
                        console.error('加载数据失败:', error);
                        ElMessage.error('加载数据失败，请检查网络连接');
                    } finally {
                        this.loading = false;
                    }
                },
                async fetchData() {
                    const requestBody = {
                         "pageSize": 1000,
                        "pageNo": 1,
                        "pageIndex": 1,
                        "page_id": "794321405799424",
                        "extendData": {},
                        "params": {},
                        "tabType": "sql",
                        "sql_id": "174b7551-2aed-49df-a2ca-030dbaa1fb02",
                        "component_id": "6604d148-7d86-30f5-6a32-45017c0ce596",
                        "filter": this.buildFilters()
                    };

                    const response = await fetch(API_CONFIG.baseUrl, {
                        method: 'POST',
                        headers: API_CONFIG.headers,
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return await response.json();
                },
                getCurrentMonth() {
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth()).padStart(2, '0');
                    return `${year}-${month}`;
                },
                buildFilters() {
                    const filters = [];

                    // 年月过滤
                    if (this.filterForm.currentMonth) {
                        const [year, month] = this.filterForm.currentMonth.split('-');

                        filters.push({
                            key: "year",
                            operator: "EQUAL",
                            value: year,
                            component: "el-date-picker"
                        });

                        filters.push({
                            key: "month",
                            operator: "EQUAL",
                            value: parseInt(month).toString(),
                            component: "el-date-picker"
                        });
                    }

                    return filters;
                },
                processDepartmentData() {
                    // 构建部门层级结构
                    const deptMap = new Map();

                    // 统计各部门的项目和投入数据
                    this.rawData.forEach(record => {
                        const deputyDept = record.deputy_dept_name || '未分类';
                        const firstDept = record.first_dept_name || '未分类';
                        if(firstDept == '未分类') {
                            debugger;
                        }

                        // 副总级部门
                        if (!deptMap.has(deputyDept)) {
                            deptMap.set(deputyDept, {
                                id: deputyDept,
                                name: deputyDept,
                                level: '副总级',
                                project_count: 0,
                                employee_count: 0,
                                total_investment: 0,
                                projects: new Set(),
                                employees: new Set(),
                                children: new Map()
                            });
                        }

                        const deputyDeptData = deptMap.get(deputyDept);
                        deputyDeptData.projects.add(record.dev_proj_id);
                        deputyDeptData.employees.add(record.emp_no);
                        deputyDeptData.total_investment += parseFloat(record.per_days || 0) / 22;

                        // 一级部门
                        if (!deputyDeptData.children.has(firstDept)) {
                            deputyDeptData.children.set(firstDept, {
                                id: `${deputyDept}-${firstDept}`,
                                name: firstDept,
                                level: '一级',
                                project_count: 0,
                                employee_count: 0,
                                total_investment: 0,
                                projects: new Set(),
                                employees: new Set(),
                                children: []
                            });
                        }

                        const firstDeptData = deputyDeptData.children.get(firstDept);
                        firstDeptData.projects.add(record.dev_proj_id);
                        firstDeptData.employees.add(record.emp_no);
                        firstDeptData.total_investment += parseFloat(record.per_days || 0) / 22;
                    });

                    // 转换为树形结构
                    this.departmentTree = Array.from(deptMap.values()).map(dept => {
                        const children = Array.from(dept.children.values()).map(child => ({
                            ...child,
                            project_count: child.projects.size,
                            employee_count: child.employees.size,
                            total_investment: child.total_investment.toFixed(1)
                        }));

                        return {
                            ...dept,
                            project_count: dept.projects.size,
                            employee_count: dept.employees.size,
                            total_investment: dept.total_investment.toFixed(1),
                            children: children
                        };
                    });
                },
                selectDepartment(dept) {
                    this.selectedDept = dept;
                    this.updateDepartmentProjects();
                    this.rankingPagination.currentPage = 1; // 重置分页
                },
                handleRankingSizeChange(val) {
                    this.rankingPagination.pageSize = val;
                    this.rankingPagination.currentPage = 1;
                },
                handleRankingCurrentChange(val) {
                    this.rankingPagination.currentPage = val;
                },
                handleEmployeeSizeChange(val) {
                    this.employeePagination.pageSize = val;
                    this.employeePagination.currentPage = 1;
                },
                handleEmployeeCurrentChange(val) {
                    this.employeePagination.currentPage = val;
                },
                selectProjectForEmployees(project) {
                    this.selectedProjectForEmployees = project;
                    this.updateProjectEmployees();
                    this.employeePagination.currentPage = 1; // 重置分页
                },
                updateProjectEmployees() {
                    if (!this.selectedProjectForEmployees || !this.selectedDept) {
                        this.projectEmployees = [];
                        return;
                    }

                    // 根据选中部门和项目过滤数据
                    const projectData = this.rawData.filter(item =>
                        (item.first_dept_name === this.selectedDept.name ||
                         item.deputy_dept_name === this.selectedDept.name) &&
                        item.dev_proj_id === this.selectedProjectForEmployees.id
                    );

                    // 按员工分组统计投入
                    const employeeMap = new Map();
                    projectData.forEach(item => {
                        const empNo = item.emp_no;
                        const empName = item.emp_name;
                        const days = parseFloat(item.per_days || 0);

                        if (!employeeMap.has(empNo)) {
                            employeeMap.set(empNo, {
                                emp_no: empNo,
                                emp_name: empName,
                                days: 0
                            });
                        }

                        employeeMap.get(empNo).days += days;
                    });

                    // 计算总投入和比例
                    const totalDays = Array.from(employeeMap.values()).reduce((sum, emp) => sum + emp.days, 0);

                    // 转换为排行榜格式并按投入降序排列
                    this.projectEmployees = Array.from(employeeMap.values())
                        .map(employee => ({
                            ...employee,
                            ratio: totalDays > 0 ? ((employee.days / totalDays) * 100).toFixed(1) : 0
                        }))
                        .sort((a, b) => b.days - a.days);
                },
                updateDepartmentProjects() {
                    if (!this.selectedDept) {
                        this.departmentProjects = [];
                        return;
                    }

                    // 根据选中部门过滤数据
                    const deptData = this.rawData.filter(item =>
                        item.first_dept_name === this.selectedDept.name ||
                        item.deputy_dept_name === this.selectedDept.name
                    );

                    // 按项目分组统计投入
                    const projectMap = new Map();
                    deptData.forEach(item => {
                        const projectId = item.dev_proj_id;
                        const projectName = item.dev_proj_name;
                        const days = parseFloat(item.per_days || 0);

                        if (!projectMap.has(projectId)) {
                            projectMap.set(projectId, {
                                id: projectId,
                                name: projectName,
                                totalDays: 0
                            });
                        }

                        projectMap.get(projectId).totalDays += days;
                    });

                    // 计算总投入和比例
                    const totalDays = Array.from(projectMap.values()).reduce((sum, project) => sum + project.totalDays, 0);

                    // 转换为排行榜格式并按投入降序排列
                    this.departmentProjects = Array.from(projectMap.values())
                        .map(project => ({
                            id: project.id,
                            name: project.name,
                            investment: (project.totalDays / 22).toFixed(1), // 转换为人月
                            ratio: totalDays > 0 ? ((project.totalDays / totalDays) * 100).toFixed(1) : 0,
                            progress: Math.min(90, Math.max(30, Math.random() * 100)) // 模拟进度
                        }))
                        .sort((a, b) => parseFloat(b.investment) - parseFloat(a.investment));
                },
                applyFilter() {
                    this.loadData();
                    ElMessage.success('筛选条件已应用');
                },
                resetFilter() {
                    this.filterForm = {
                        currentMonth: this.getCurrentMonth(),
                        deptLevel: '',
                        projectStatus: '',
                        search: ''
                    };
                    this.loadData();
                },
                initCharts() {
                    this.initProjectChart();
                    this.initEfficiencyChart();
                },
                updateCharts() {
                    if (this.projectChart && this.efficiencyChart) {
                        this.updateProjectChart();
                        this.updateEfficiencyChart();
                    }
                },
                initProjectChart() {
                    const chartDom = document.getElementById('deptProjectChart');
                    this.projectChart = echarts.init(chartDom);
                    this.updateProjectChart();
                },
                updateProjectChart() {
                    if (!this.selectedDept) return;

                    // 根据选中部门生成项目分布数据
                    let data = [];
                    if (this.selectedDept.children && this.selectedDept.children.length > 0) {
                        data = this.selectedDept.children.map(child => ({
                            value: child.project_count,
                            name: child.name
                        }));
                    } else {
                        // 如果没有子部门，显示项目类型分布
                        data = [
                            { value: Math.ceil(this.selectedDept.project_count * 0.4), name: '产品开发' },
                            { value: Math.ceil(this.selectedDept.project_count * 0.3), name: '技术优化' },
                            { value: Math.ceil(this.selectedDept.project_count * 0.2), name: '基础设施' },
                            { value: Math.ceil(this.selectedDept.project_count * 0.1), name: '其他' }
                        ];
                    }

                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}个项目 ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left'
                        },
                        series: [
                            {
                                name: '项目分布',
                                type: 'pie',
                                radius: '55%',
                                center: ['60%', '50%'],
                                data: data,
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };

                    this.projectChart.setOption(option);
                },
                initEfficiencyChart() {
                    const chartDom = document.getElementById('deptEfficiencyChart');
                    this.efficiencyChart = echarts.init(chartDom);
                    this.updateEfficiencyChart();
                },
                updateEfficiencyChart() {
                    const option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        legend: {
                            data: ['项目完成率', '人均产出', '质量指标']
                        },
                        radar: {
                            indicator: [
                                { name: '前端组', max: 100 },
                                { name: '后端组', max: 100 },
                                { name: '测试组', max: 100 },
                                { name: 'UI组', max: 100 },
                                { name: '产品组', max: 100 }
                            ]
                        },
                        series: [
                            {
                                name: '部门效率',
                                type: 'radar',
                                data: [
                                    {
                                        value: [85, 92, 78, 88, 90],
                                        name: '本月效率'
                                    },
                                    {
                                        value: [80, 88, 75, 85, 87],
                                        name: '上月效率'
                                    }
                                ]
                            }
                        ]
                    };

                    this.efficiencyChart.setOption(option);
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
