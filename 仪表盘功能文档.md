# 研发中心仪表盘功能清单

## 概述
基于现有数据结构，为研发中心负责人设计的综合管理仪表盘，涵盖任务管理、人员管理、项目管理、资源配置等核心功能。

## 功能清单

### 1. 📊 总览仪表盘
**功能描述**：研发中心整体运营状况概览
**数据计算方式**：
```javascript
// 项目总数统计
const projectStats = {
  total: devProjects.length,
  inProgress: devProjects.filter(p => p.dev_proj_status === 'in_prog').length,
  completed: devProjects.filter(p => p.dev_proj_status === 'completed').length,
  paused: devProjects.filter(p => p.dev_proj_status === 'paused').length
}

// 人员总数统计
const staffStats = {
  total: uniqueStaff.length,
  byDept: groupBy(dailyReports, 'first_dept_name'),
  byType: groupBy(dailyReports, 'emp_type')
}

// 投入成本统计
const costStats = {
  totalBudget: sum(devProjects, 'dev_proj_cost'),
  totalIncome: sum(devProjects, 'funi_proj_income'),
  totalManMonth: sum(devProjects, 'dev_proj_imm')
}
```

### 2. 📋 任务维度管理
**功能描述**：部门下所有人的任务完成情况、进度、风险监控
**数据计算方式**：
```javascript
// 任务完成情况统计
const taskStats = {
  // 从计划明细表获取任务数据
  totalTasks: planDetails.length,
  completedTasks: planDetails.filter(p => p.status === 'completed').length,
  inProgressTasks: planDetails.filter(p => p.status === 'in_progress').length,
  delayedTasks: planDetails.filter(p => 
    new Date(p.plan_end_date) < new Date() && p.status !== 'completed'
  ).length,
  
  // 进度计算
  avgProgress: average(planDetails, 'schedule'),
  
  // 风险识别
  riskTasks: planDetails.filter(p => {
    const planEndDate = new Date(p.plan_end_date);
    const today = new Date();
    const daysLeft = (planEndDate - today) / (1000 * 60 * 60 * 24);
    return daysLeft < 3 && p.schedule < 80; // 3天内到期且进度<80%
  })
}

// 按人员分组的任务统计
const tasksByPerson = groupBy(planDetails, 'creator_name').map(group => ({
  person: group.key,
  totalTasks: group.items.length,
  completedTasks: group.items.filter(t => t.status === 'completed').length,
  avgProgress: average(group.items, 'schedule'),
  workload: sum(group.items, 'estimate_man_hour')
}));
```

### 3. 👥 人员维度管理
**功能描述**：查看人员在哪些项目中的参与情况
**数据计算方式**：
```javascript
// 人员项目参与情况
const staffProjectMapping = dailyReportDetails.reduce((acc, detail) => {
  const staffKey = detail.creator_name;
  if (!acc[staffKey]) {
    acc[staffKey] = {
      name: staffKey,
      dept: detail.deputy_dept_name,
      projects: new Set(),
      totalDaysInvested: 0,
      activeProjects: []
    };
  }
  
  acc[staffKey].projects.add(detail.dev_proj_name);
  acc[staffKey].totalDaysInvested += detail.days_invested;
  
  // 记录活跃项目详情
  const existingProject = acc[staffKey].activeProjects.find(p => 
    p.projectId === detail.dev_proj_id
  );
  
  if (existingProject) {
    existingProject.daysInvested += detail.days_invested;
  } else {
    acc[staffKey].activeProjects.push({
      projectId: detail.dev_proj_id,
      projectName: detail.dev_proj_name,
      daysInvested: detail.days_invested,
      jobContent: detail.job_content
    });
  }
  
  return acc;
}, {});
```

### 4. 🎯 项目维度管理
**功能描述**：查看项目中投入的人员清单及投入比例
**数据计算方式**：
```javascript
// 项目人员投入分析
const projectStaffAnalysis = devProjects.map(project => {
  // 获取该项目的所有日报详情
  const projectDetails = dailyReportDetails.filter(d => 
    d.dev_proj_id === project.id
  );
  
  // 按人员分组统计投入
  const staffInvestment = groupBy(projectDetails, 'creator_name').map(group => {
    const totalDays = sum(group.items, 'days_invested');
    
    // 计算该人员在所有项目的总投入
    const personAllProjects = dailyReportDetails.filter(d => 
      d.creator_name === group.key
    );
    const personTotalDays = sum(personAllProjects, 'days_invested');
    
    return {
      name: group.key,
      dept: group.items[0].deputy_dept_name,
      daysInProject: totalDays,
      totalDaysAllProjects: personTotalDays,
      investmentRatio: (totalDays / personTotalDays * 100).toFixed(2) + '%',
      recentActivities: group.items.slice(-5) // 最近5条工作记录
    };
  });
  
  return {
    projectId: project.id,
    projectName: project.dev_proj_name,
    projectManager: project.dev_proj_pm,
    totalStaff: staffInvestment.length,
    totalDaysInvested: sum(staffInvestment, 'daysInProject'),
    staffDetails: staffInvestment,
    projectStatus: project.dev_proj_status,
    budget: project.dev_proj_cost,
    expectedIncome: project.funi_proj_income
  };
});
```

### 5. ⚖️ 人员工作饱和度分析
**功能描述**：查看人员工作饱和度，识别过载情况
**数据计算方式**：
```javascript
// 人员工作饱和度计算
const staffWorkloadAnalysis = Object.values(staffProjectMapping).map(staff => {
  // 计算月度工作饱和度（假设每月22个工作日）
  const monthlyWorkDays = 22;
  const currentMonth = new Date().getMonth();
  
  // 获取当月的工作投入
  const currentMonthDetails = dailyReportDetails.filter(d => 
    d.creator_name === staff.name && 
    new Date(d.create_time).getMonth() === currentMonth
  );
  
  const monthlyDaysInvested = sum(currentMonthDetails, 'days_invested');
  const saturationRate = (monthlyDaysInvested / monthlyWorkDays * 100).toFixed(2);
  
  // 获取计划工时
  const staffPlans = planDetails.filter(p => p.creator_name === staff.name);
  const plannedHours = sum(staffPlans, 'estimate_man_hour');
  const actualHours = sum(staffPlans, 'actual_man_hour');
  
  return {
    name: staff.name,
    dept: staff.dept,
    monthlyDaysInvested,
    saturationRate: parseFloat(saturationRate),
    isOverloaded: parseFloat(saturationRate) > 100,
    plannedHours,
    actualHours,
    efficiency: actualHours > 0 ? (plannedHours / actualHours * 100).toFixed(2) + '%' : 'N/A',
    projectCount: staff.projects.size,
    riskLevel: parseFloat(saturationRate) > 120 ? 'high' : 
               parseFloat(saturationRate) > 100 ? 'medium' : 'low'
  };
});
```

### 6. 📅 人员忙闲状态日历
**功能描述**：查看人员在时间跨度中的具体忙闲状态
**数据计算方式**：
```javascript
// 人员日程状态分析
const generateStaffCalendar = (staffName, startDate, endDate) => {
  const calendar = [];
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
    const dateStr = date.toISOString().split('T')[0];
    
    // 检查该日期的日报
    const dailyReport = dailyReports.find(r => 
      r.creator_name === staffName && 
      r.daily_date === dateStr
    );
    
    // 检查该日期的计划任务
    const dayTasks = planDetails.filter(p => 
      p.creator_name === staffName &&
      dateStr >= p.plan_begin_date && 
      dateStr <= p.plan_end_date
    );
    
    calendar.push({
      date: dateStr,
      hasReport: !!dailyReport,
      hasTasks: dayTasks.length > 0,
      taskCount: dayTasks.length,
      completedTasks: dayTasks.filter(t => t.status === 'completed').length,
      workload: sum(dayTasks, 'estimate_man_hour'),
      status: dailyReport ? 'busy' : (dayTasks.length > 0 ? 'scheduled' : 'free'),
      activities: dailyReport ? [dailyReport.daily_titile] : [],
      tasks: dayTasks.map(t => ({
        content: t.job_content,
        status: t.status,
        progress: t.schedule
      }))
    });
  }
  
  return calendar;
};
```

### 7. 📈 项目进度监控
**功能描述**：项目进度跟踪和里程碑管理
**数据计算方式**：
```javascript
// 项目进度分析
const projectProgressAnalysis = devProjects.map(project => {
  const projectPlans = planDetails.filter(p => p.dev_proj_id === project.id);
  const projectReports = dailyReportDetails.filter(d => d.dev_proj_id === project.id);
  
  // 计算项目整体进度
  const avgProgress = projectPlans.length > 0 ? 
    average(projectPlans, 'schedule') : 0;
  
  // 计算时间进度
  const startDate = new Date(project.funi_proj_begin_date);
  const endDate = new Date(project.funi_proj_end_date);
  const today = new Date();
  const totalDuration = endDate - startDate;
  const elapsedDuration = today - startDate;
  const timeProgress = (elapsedDuration / totalDuration * 100).toFixed(2);
  
  // 风险评估
  const scheduleRisk = avgProgress < parseFloat(timeProgress) - 10;
  
  return {
    projectId: project.id,
    projectName: project.dev_proj_name,
    workProgress: avgProgress,
    timeProgress: parseFloat(timeProgress),
    isDelayed: scheduleRisk,
    totalTasks: projectPlans.length,
    completedTasks: projectPlans.filter(p => p.status === 'completed').length,
    activeDays: projectReports.length,
    totalInvestment: sum(projectReports, 'days_invested'),
    riskLevel: scheduleRisk ? 'high' : (avgProgress < 50 ? 'medium' : 'low')
  };
});
```

### 8. 💰 成本效益分析
**功能描述**：项目成本投入与收益分析
**数据计算方式**：
```javascript
// 成本效益分析
const costBenefitAnalysis = devProjects.map(project => {
  const projectReports = dailyReportDetails.filter(d => d.dev_proj_id === project.id);
  const actualInvestment = sum(projectReports, 'days_invested');

  // 假设日均成本（可配置）
  const dailyCost = 800; // 元/人天
  const actualCost = actualInvestment * dailyCost;

  return {
    projectName: project.dev_proj_name,
    budgetCost: project.dev_proj_cost || 0,
    actualCost,
    expectedIncome: project.funi_proj_income || 0,
    costVariance: ((actualCost - project.dev_proj_cost) / project.dev_proj_cost * 100).toFixed(2) + '%',
    roi: project.funi_proj_income > 0 ?
      ((project.funi_proj_income - actualCost) / actualCost * 100).toFixed(2) + '%' : 'N/A',
    profitMargin: project.funi_proj_income > 0 ?
      ((project.funi_proj_income - actualCost) / project.funi_proj_income * 100).toFixed(2) + '%' : 'N/A'
  };
});
```

### 9. 🏢 部门资源配置
**功能描述**：部门间资源分配和协调
**数据计算方式**：
```javascript
// 部门资源配置分析
const deptResourceAllocation = groupBy(dailyReports, 'first_dept_name').map(dept => {
  const deptStaff = dept.items;
  const deptProjects = [...new Set(
    dailyReportDetails
      .filter(d => deptStaff.some(s => s.creator_name === d.creator_name))
      .map(d => d.dev_proj_id)
  )];

  return {
    deptName: dept.key,
    staffCount: deptStaff.length,
    projectCount: deptProjects.length,
    totalWorkload: sum(deptStaff.map(s =>
      planDetails.filter(p => p.creator_name === s.creator_name)
    ).flat(), 'estimate_man_hour'),
    avgSaturation: average(
      staffWorkloadAnalysis.filter(s =>
        deptStaff.some(ds => ds.creator_name === s.name)
      ),
      'saturationRate'
    )
  };
});
```

### 10. 📊 数据导出与报表
**功能描述**：生成各类管理报表
**数据计算方式**：
```javascript
// 报表数据生成
const generateReports = {
  weekly: () => generateWeeklyReport(staffWorkloadAnalysis, projectProgressAnalysis),
  monthly: () => generateMonthlyReport(costBenefitAnalysis, deptResourceAllocation),
  quarterly: () => generateQuarterlyReport(projectStats, staffStats),
  custom: (startDate, endDate) => generateCustomReport(startDate, endDate)
};
```

## 数据关联关系图

```
日报表 ←→ 日报详情表 (daily_info)
    ↓
员工信息 ←→ 计划管理 ←→ 计划明细
    ↓           ↓
研发项目 ←→ 项目产品关系 ←→ 产品信息
    ↓
区域信息
```

## 工具函数说明

### 数据聚合函数
```javascript
// 分组函数
const groupBy = (array, key) => {
  return array.reduce((groups, item) => {
    const group = item[key];
    if (!groups[group]) {
      groups[group] = { key: group, items: [] };
    }
    groups[group].items.push(item);
    return groups;
  }, {});
};

// 求和函数
const sum = (array, key) => {
  return array.reduce((total, item) => total + (item[key] || 0), 0);
};

// 平均值函数
const average = (array, key) => {
  if (array.length === 0) return 0;
  return sum(array, key) / array.length;
};

// 去重函数
const unique = (array, key) => {
  return array.filter((item, index, self) =>
    index === self.findIndex(t => t[key] === item[key])
  );
};
```

## 技术实现要点

### 1. 数据获取策略
- 使用现有API接口获取基础数据
- 前端进行数据聚合和计算
- 实现数据缓存机制提升性能

### 2. 实时更新机制
- 定时刷新关键数据
- 支持手动刷新功能
- 数据变更提醒

### 3. 性能优化
- 分页加载大数据集
- 虚拟滚动处理长列表
- 图表数据采样显示

### 4. 用户体验
- 响应式设计适配不同屏幕
- 交互式图表支持钻取
- 导出功能支持多种格式

## 部署说明

### 文件结构
```
dashboard/
├── index.html          # 主页面
├── css/
│   └── style.css      # 样式文件
├── js/
│   ├── main.js        # 主逻辑
│   ├── api.js         # API接口
│   ├── utils.js       # 工具函数
│   └── charts.js      # 图表配置
└── assets/            # 静态资源
```

### 环境要求
- 现代浏览器支持ES6+
- 网络访问API接口
- 无需服务器端部署

这个功能清单涵盖了研发中心负责人需要的核心管理功能，通过数据聚合和计算提供全面的决策支持信息。
