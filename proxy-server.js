const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3000;

// 启用CORS
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Funipaas-Authorization', 'X-Funipaas-Request-Hash', 'X-Funipaas-Request-Id', 'X-Funipaas-Tenant', 'X-Requested-With'],
    credentials: false
}));

// 处理预检请求
app.options('*', (req, res) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS,HEAD');
    res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Funipaas-Authorization,X-Funipaas-Request-Hash,X-Funipaas-Request-Id,X-Funipaas-Tenant,X-Requested-With');
    res.sendStatus(200);
});

// 静态文件服务 - 提供HTML文件
app.use(express.static(__dirname));

// API代理中间件
const apiProxy = createProxyMiddleware('/api', {
    target: 'https://erm.funi.com',
    changeOrigin: true,
    secure: true,
    pathRewrite: {
        '^/api': '/jyxt/as/app_4kuz6qjvob6/sql/list'
    },
    onProxyReq: (proxyReq, req, res) => {
        console.log('代理请求:', req.method, req.url);
        
        // 保持原有的请求头
        if (req.headers['x-funipaas-authorization']) {
            proxyReq.setHeader('X-Funipaas-Authorization', req.headers['x-funipaas-authorization']);
        }
        if (req.headers['x-funipaas-request-hash']) {
            proxyReq.setHeader('X-Funipaas-Request-Hash', req.headers['x-funipaas-request-hash']);
        }
        if (req.headers['x-funipaas-request-id']) {
            proxyReq.setHeader('X-Funipaas-Request-Id', req.headers['x-funipaas-request-id']);
        }
        if (req.headers['x-funipaas-tenant']) {
            proxyReq.setHeader('X-Funipaas-Tenant', req.headers['x-funipaas-tenant']);
        }
        
        // 确保Content-Type
        proxyReq.setHeader('Content-Type', 'application/json');
    },
    onProxyRes: (proxyRes, req, res) => {
        console.log('代理响应:', proxyRes.statusCode, req.url);
        
        // 添加CORS头
        proxyRes.headers['Access-Control-Allow-Origin'] = '*';
        proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,POST,PUT,DELETE,OPTIONS';
        proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type,Authorization,X-Funipaas-Authorization,X-Funipaas-Request-Hash,X-Funipaas-Request-Id,X-Funipaas-Tenant';
    },
    onError: (err, req, res) => {
        console.error('代理错误:', err.message);
        res.status(500).json({
            error: '代理服务器错误',
            message: err.message
        });
    }
});

// 使用代理中间件
app.use(apiProxy);

// 健康检查接口
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        message: '代理服务器运行正常',
        timestamp: new Date().toISOString()
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log('🚀 代理服务器启动成功!');
    console.log('================================');
    console.log(`📡 服务器地址: http://localhost:${PORT}`);
    console.log('🌐 页面访问地址:');
    console.log(`   人员分析: http://localhost:${PORT}/person-analysis.html`);
    console.log(`   项目分析: http://localhost:${PORT}/project-analysis.html`);
    console.log(`   部门分析: http://localhost:${PORT}/department-analysis.html`);
    console.log(`   API测试:  http://localhost:${PORT}/api-test.html`);
    console.log('🔗 API代理地址:');
    console.log(`   代理接口: http://localhost:${PORT}/api`);
    console.log(`   目标接口: https://erm.funi.com/jyxt/as/app_4kuz6qjvob6/sql/list`);
    console.log(`   健康检查: http://localhost:${PORT}/health`);
    console.log('================================');
    console.log('💡 使用说明:');
    console.log('1. 在HTML页面中将API地址改为: http://localhost:3000/api');
    console.log('2. 保持原有的请求头和请求体不变');
    console.log('3. 按 Ctrl+C 停止服务器');
    console.log('================================');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭代理服务器...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 正在关闭代理服务器...');
    process.exit(0);
});
